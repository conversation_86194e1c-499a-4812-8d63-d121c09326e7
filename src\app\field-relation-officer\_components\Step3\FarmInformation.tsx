import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { farmInformationSchema, TFarmInformationSchema } from '../../schemas';
import { IFarmInformation, IFROState, ISafeParseResult } from '../../types';
import FarmLocation from './FarmLocation';
import FarmPractices from './FarmPractices';

interface IFarmInformationProps {
  gStateFRO: State<IFROState, {}>;
  onPrevious: () => void;
  onNext: () => void;
  onClear: () => void;
}

const FarmInformation = ({ gStateFRO, onPrevious, onNext, onClear }: IFarmInformationProps) => {
  const data = gStateFRO?.form.step3?.value;
  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TFarmInformationSchema>({
    resolver: zodResolver(farmInformationSchema),
    defaultValues: {
      // Farm Location
      farmAddressHouseNumber: data?.farmAddressHouseNumber || '',
      farmAddressStreet: data?.farmAddressStreet || '',
      farmAddressRegion: data?.farmAddressRegion || '',
      farmAddressProvince: data?.farmAddressProvince || '',
      farmAddressCity: data?.farmAddressCity || '',
      farmAddressBarangay: data?.farmAddressBarangay || '',
      farmAddressZipCode: data?.farmAddressZipCode || '',
      farmArea: data?.farmArea || '',
      farmOwnership: data?.farmOwnership || '',
      otherFarmOwnership: data?.otherFarmOwnership || '',
      cropsPlanted: data?.cropsPlanted?.slice() || [],

      // Farm Practices
      waterSource: data?.waterSource || '',
      waterSourceOthers: data?.waterSource.includes(', Others') ? data?.waterSourceOthers || '' : '',
      fertilizerUsed: data?.fertilizerUsed || '',
      pesticideUsed: data?.pesticideUsed || '',
      farmImplements: data?.farmImplements || '',
      farmImplementsOthers: data?.farmImplements.includes(', Others') ? data?.farmImplementsOthers || '' : '',
    },
  });

  const handleNext = handleSubmit(() => {
    const { data, success } = farmInformationSchema.safeParse(values()) as ISafeParseResult<IFarmInformation>;

    if (success) {
      gStateFRO.form.step3.set(data);
      onNext();
    }
  });

  const handlePrevious = () => {
    const { data, success } = farmInformationSchema.safeParse(values()) as ISafeParseResult<IFarmInformation>;

    if (success) {
      gStateFRO.form.step3.set(data);
    }
    onPrevious();
  };

  return (
    <form onSubmit={handleNext}>
      <FarmLocation register={register} control={control} errors={errors} values={values} watch={watch} />
      <FarmPractices control={control} errors={errors} />

      <div className="mt-16 flex justify-between">
        <Button type="button" variant="outline" onClick={onClear}>
          Clear All Forms
        </Button>
        <div className="flex justify-center gap-4">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit">Next</Button>
        </div>
      </div>
    </form>
  );
};

export default FarmInformation;
