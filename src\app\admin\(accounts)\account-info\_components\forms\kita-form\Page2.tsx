import { Check } from 'lucide-react';
import React from 'react';

const Page2 = ({ data }) => {
  let farm = false;
  let salaryHonoraria = false;
  let interestCommission = false;
  let otherBusinessIncome = false;
  let pension = false;
  let ofwRemittance = false;
  let otherRemittance = false;
  (data?.source_of_funds?.value || '').split(',').map((crop) => {
    if (crop === 'farm') {
      farm = true;
    } else if (crop === 'SALARY/HONORARIA') {
      salaryHonoraria = true;
    } else if (crop === 'INTEREST/COMMISSION') {
      interestCommission = true;
    } else if (crop === 'BUSINESS') {
      otherBusinessIncome = true;
    } else if (crop === 'PENSION') {
      pension = true;
    } else if (crop === 'OFW REMITTANCE') {
      ofwRemittance = true;
    } else if (crop === 'OTHER REMITTANCE') {
      otherRemittance = true;
    }
  });

  return (
    <div className="relative flex h-[14in] w-[8.5in] flex-col border bg-[url(/assets/forms/kita-form/kita-form-2.jpg)] bg-contain bg-top bg-no-repeat capitalize print:border-none">
      {/* Monthly Income */}
      <div className="absolute top-[6.2rem] flex">
        {/* Source of funds */}
        {/* Farm */}
        <Check strokeWidth={5} className={`absolute flex text-sm ${farm ? 'left-[14.6rem] top-[-3px]' : 'hidden'}`} />
        {/* Other Business Income */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${otherBusinessIncome ? 'left-[14.6rem] top-[1.2rem]' : 'hidden'}`}
        />
        {/* Salary Honoraria */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${salaryHonoraria ? 'left-[26.4rem] top-[-3px]' : 'hidden'}`}
        />
        {/* Interest Commission */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${interestCommission ? 'left-[26.4rem] top-[1.2rem]' : 'hidden'}`}
        />
        {/* OFW Remittance */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${ofwRemittance ? 'left-[37.1rem] top-[-3px]' : 'hidden'}`}
        />
        {/* Other Remittance */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${otherRemittance ? 'left-[37.1rem] top-[1.2rem]' : 'hidden'}`}
        />

        <div className="absolute left-[12.6rem] top-[3.2rem]">
          {Number(Math.round(Number(data?.farmerInfo?.monthly_gross_income?.value))).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </div>
      </div>
    </div>
  );
};

export default Page2;
