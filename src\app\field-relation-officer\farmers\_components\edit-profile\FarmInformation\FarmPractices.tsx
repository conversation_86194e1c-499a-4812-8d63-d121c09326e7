'use client';

import { useEffect } from 'react';
import { Control, Controller, FieldErrors } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import {
  FARM_IMPLEMENTS,
  FERTILIZER_CHEMICAL_USED_OPTIONS,
  WATER_SOURCE,
} from '@/app/field-relation-officer/constants';
import { TFarmInformationSchema } from '@/app/field-relation-officer/schemas';
import usePublic from '@/lib/hooks/usePublic';
import { cn } from '@/lib/utils';

interface IFarmPracticesProps {
  control: Control<TFarmInformationSchema>;
  errors: FieldErrors<TFarmInformationSchema>;
}

const FarmPractices = ({ control, errors }: IFarmPracticesProps) => {
  const { getAllCrops } = usePublic();

  useEffect(() => {
    getAllCrops();
  }, []);

  return (
    <div className="mt-6">
      <FormTitle title="Farm Practices" />
      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        {/* Farm Practices */}
        <FormField name="waterSource" label="Water Source" errors={errors}>
          <Controller
            control={control}
            name="waterSource"
            render={({ field: { onChange, value } }) => {
              const existingValues = Array.isArray(value) ? value : (value || '').split(',').map((v) => v.trim());
              const currentOptions = existingValues.filter((v) => v).map((v) => ({ label: v, value: v }));

              const mergedOptions = [
                ...WATER_SOURCE,
                ...currentOptions.filter(
                  (opt) => opt.value && opt.label && !WATER_SOURCE.some((def) => def.value === opt.value),
                ),
              ];
              return (
                <MultipleSelector
                  value={currentOptions}
                  onChange={(selected) => onChange(selected.map((item) => item.value))}
                  defaultOptions={mergedOptions}
                  placeholder="Select from selection or create new"
                  creatable
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              );
            }}
          />
        </FormField>
        <FormField name="fertilizerUsed" label="Fertilizer Used" errors={errors}>
          <Controller
            control={control}
            name="fertilizerUsed"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className={cn(errors.fertilizerUsed && 'border-red-500')}>
                  <SelectValue placeholder="Select Fertilizer Type" />
                </SelectTrigger>
                <SelectContent>
                  {FERTILIZER_CHEMICAL_USED_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        <FormField name="pesticideUsed" label="Pesticide Used" errors={errors}>
          <Controller
            control={control}
            name="pesticideUsed"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className={cn(errors.pesticideUsed && 'border-red-500')}>
                  <SelectValue placeholder="Select Pesticide Type" />
                </SelectTrigger>
                <SelectContent>
                  {FERTILIZER_CHEMICAL_USED_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        <FormField name="farmImplements" label="Farm Implements" errors={errors}>
          <Controller
            control={control}
            name="farmImplements"
            render={({ field: { onChange, value } }) => {
              const existingValues = Array.isArray(value) ? value : (value || '').split(',').map((v) => v.trim());
              const currentOptions = existingValues.filter((v) => v).map((v) => ({ label: v, value: v }));

              const mergedOptions = [
                ...FARM_IMPLEMENTS,
                ...currentOptions.filter(
                  (opt) => opt.value && opt.label && !FARM_IMPLEMENTS.some((def) => def.value === opt.value),
                ),
              ];
              return (
                <MultipleSelector
                  value={currentOptions}
                  onChange={(selected) => onChange(selected.map((item) => item.value))}
                  defaultOptions={mergedOptions}
                  placeholder="Select from selection or create new"
                  creatable
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              );
            }}
          />
        </FormField>
      </div>
    </div>
  );
};

export default FarmPractices;
