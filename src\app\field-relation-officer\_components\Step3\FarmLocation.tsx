'use client';

import { useEffect } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  useFormContext,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import usePublic from '@/lib/hooks/usePublic';
import useSelectAddress from '@/lib/hooks/utils/useSelectAddress';
import { cn } from '@/lib/utils';

import { TFarmInformationSchema } from '../../schemas';

interface IFarmLocationProps {
  register: UseFormRegister<TFarmInformationSchema>;
  control: Control<TFarmInformationSchema>;
  errors: FieldErrors<TFarmInformationSchema>;
  values: UseFormGetValues<TFarmInformationSchema>;
  watch: UseFormWatch<TFarmInformationSchema>;
}

const FarmLocation = ({ register, control, errors, values, watch }: IFarmLocationProps) => {
  const { getAllCrops, OPTION_CROPS_PLANTED } = usePublic();
  const address = useSelectAddress();
  const {
    setStreet,
    setRegionSelected,
    regionList,
    setProvinceSelected,
    provinceList,
    setCitySelected,
    cityList,
    setBarangaySelected,
    barangayList,
    setPostalCode,
  } = address;

  useEffect(() => {
    if (values().farmAddressRegion) {
      setRegionSelected(JSON.parse(values().farmAddressRegion));
    }
    if (values().farmAddressProvince) {
      setProvinceSelected(JSON.parse(values().farmAddressProvince));
    }
    if (values().farmAddressCity) {
      setCitySelected(JSON.parse(values().farmAddressCity));
    }
    if (values().farmAddressBarangay) {
      setBarangaySelected(JSON.parse(values().farmAddressBarangay));
    }
  }, [
    values().farmAddressRegion,
    values().farmAddressProvince,
    values().farmAddressCity,
    values().farmAddressBarangay,
  ]);

  const farmOwnership = watch('farmOwnership');

  useEffect(() => {
    getAllCrops();
  }, []);

  return (
    <div className="mt-6">
      <FormTitle title="Farm Location" />
      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        {/* farmAddressHouseNumber */}
        <FormField name="farmAddressHouseNumber" label="House No./Lot/Bldg/Purok" errors={errors} required>
          <Controller
            control={control}
            name="farmAddressHouseNumber"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter House No."
                onChange={(e) => {
                  field.onChange(e.target.value);
                  address.setStreet(e.target.value);
                }}
                className={cn('focus-visible:ring-primary', errors.farmAddressHouseNumber && 'border-red-500')}
              />
            )}
          />
        </FormField>
        {/* farmAddressStreet */}
        <FormField name="farmAddressStreet" label="Street/Sitio/Subdivision" errors={errors} required>
          <Input
            {...register('farmAddressStreet', { required: 'Required' })}
            placeholder="Enter Street"
            className={cn(errors.farmAddressStreet && 'border-red-500')}
          />
        </FormField>
        {/* farmAddressRegion */}
        <FormField name="farmAddressRegion" label="Region" errors={errors} required>
          <Controller
            control={control}
            name="farmAddressRegion"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) address.setRegionSelected(JSON.parse(v));
                }}
                value={field.value}
              >
                <SelectTrigger className={cn(errors.farmAddressRegion && 'border-red-500')}>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.regionList.map((region: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(region)}>
                        {region.region_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        {/* farmAddressProvince */}
        <FormField name="farmAddressProvince" label="Province" errors={errors} required>
          <Controller
            control={control}
            name="farmAddressProvince"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) address.setProvinceSelected(JSON.parse(v));
                }}
                value={field.value}
                disabled={!address.provinceList.length}
              >
                <SelectTrigger className={cn(errors.farmAddressProvince && 'border-red-500')}>
                  <SelectValue placeholder="Select province" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.provinceList.map((prov: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(prov)}>
                        {prov.province_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        {/* farmAddressCity */}
        <FormField name="farmAddressCity" label="City/Municipality" errors={errors} required>
          <Controller
            control={control}
            name="farmAddressCity"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) address.setCitySelected(JSON.parse(v));
                }}
                value={field.value}
                disabled={!address.cityList.length}
              >
                <SelectTrigger className={cn(errors.farmAddressCity && 'border-red-500')}>
                  <SelectValue placeholder="Select city" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.cityList.map((city: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(city)}>
                        {city.city_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        {/* farmAddressBarangay */}
        <FormField name="farmAddressBarangay" label="Barangay" errors={errors} required>
          <Controller
            control={control}
            name="farmAddressBarangay"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) address.setBarangaySelected(JSON.parse(v));
                }}
                value={field.value}
                disabled={!address.barangayList.length}
              >
                <SelectTrigger className={cn(errors.farmAddressBarangay && 'border-red-500')}>
                  <SelectValue placeholder="Select Barangay" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.barangayList.map((bgy: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(bgy)}>
                        {bgy.brgy_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        {/* farmAddressZipCode */}
        <FormField name="farmAddressZipCode" label="Zip Code" errors={errors}>
          <Controller
            control={control}
            name="farmAddressZipCode"
            rules={{
              pattern: {
                value: /^\d{4}$/,
                message: 'Must be 4 digits (e.g., 1234)',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter Zip Code"
                onChange={(e) => {
                  field.onChange(e.target.value);
                  address.setPostalCode(e.target.value);
                }}
                className={cn(errors.farmAddressZipCode && 'border-red-500')}
                type="number"
                min={0}
              />
            )}
          />
        </FormField>
        {/* farmArea */}
        <FormField name="farmArea" label="Farm Area in Hectares" errors={errors} required>
          <Input
            {...register('farmArea', {
              required: true,
              validate: {
                isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Farm Area must be greater than 0' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.farmArea && 'border-red-500 focus-visible:ring-red-500')}
            type="number"
            min={0}
            placeholder="Enter Farm Area (hectare/s)"
          />
        </FormField>
        {/* farmOwnership */}
        <FormField name="farmOwnership" label="Land Ownership" errors={errors} required>
          <Controller
            control={control}
            name="farmOwnership"
            render={({ field: { onChange, value } }) => (
              <>
                <Select onValueChange={onChange} value={value}>
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.farmOwnership && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select Farm Ownership" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="0">RENTED / LEASED</SelectItem>
                      <SelectItem value="1">OWNED</SelectItem>
                      <SelectItem value="3">OTHERS</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </>
            )}
          />
        </FormField>

        {/* otherFarmOwnership */}
        {farmOwnership === '3' && (
          <FormField name="otherFarmOwnership" label="Others Land Ownership" errors={errors} required>
            <Controller
              control={control}
              name="otherFarmOwnership"
              rules={{ required: 'Required' }}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Please specify other ownership"
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    address.setStreet(e.target.value);
                  }}
                  className={cn('focus-visible:ring-primary', errors.farmAddressHouseNumber && 'border-red-500')}
                />
              )}
            />
          </FormField>
        )}
      </div>
      {/* Crops Planted */}
      {OPTION_CROPS_PLANTED.length > 0 && (
        <FormField name="cropsPlanted" label="Crops Planted" errors={errors} className="mt-8 max-w-full" required>
          <Controller
            control={control}
            name="cropsPlanted"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={OPTION_CROPS_PLANTED.filter((opt) => value?.includes(opt.value))}
                onChange={(selected) => onChange(selected.map((item) => item.value))}
                defaultOptions={OPTION_CROPS_PLANTED}
                placeholder="Select from selection"
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>
      )}
    </div>
  );
};

export default FarmLocation;
