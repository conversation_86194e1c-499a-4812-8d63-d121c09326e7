'use client';

import { toast } from 'sonner';

import { IResponse } from '@/app/field-relation-officer/types';
import axios from '@/lib/api';
import { useGlobalState } from '@/lib/store';

export default function useFarmer() {
  const gState = useGlobalState();

  const addFarmer = async (data: any) => {
    try {
      const res = await axios
        .post<IResponse>(`/fro/farmer/register`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      await getFarmers();

      toast.success('Success', {
        description: 'Farmer added successfully',
        duration: 3000,
      });
      return res;
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getFarmers = async () => {
    try {
      const _farmers = await axios.get(`/fro/farmer/viewAll`).then((res) => res.data.data);
      gState.fro.farmers.data.set(_farmers.data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getFarmerById = async (id: string) => {
    try {
      const response = await axios.get(`/fro/farmer/view/${id}`);
      const farmer = response.data.data;

      gState.fro.farmers.edit.set(farmer);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateFarmer = async (data) => {
    const farmerId = gState.fro.farmers.edit.id.value;
    try {
      await axios
        .post(
          '/fro/farmer/update',
          { ...data, userId: farmerId },
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        )
        .then((res) => res.data);
      await Promise.all([getFarmers(), getFarmerById(farmerId.toString())]);

      toast.success('Success', {
        description: 'Farmer updated successfully',
        duration: 3000,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateFarmer: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    addFarmer,
    getFarmers,
    getFarmerById,
    updateFarmer,
  };
}
