import React from 'react';

import { FormDimensions } from '@/lib/constants/enums';

const Page1 = ({ data, farmPlan }) => {
  const currentDate = new Date();

  console.log('farmPlan', farmPlan);
  const farmPlanItems = farmPlan[0]?.farmPlanItems || [];

  // farmer input
  const contingency = farmPlan[0]?.contingency_for_fluctuation || 0;
  const farmerInput = [
    'Seed / Seedling Requirements (SE)',
    'Soil Fertilization - Basal (Top-Dress) (FE)',
    'Soil Fertilization - Additional (Side-Dress) (FE)',
    'Foliar Fertilization (Spray) (FE)',
    'Pesticide Application (Spray / Spread) (CP)',
    'Farm Materials, Consumables, etc.',
  ];

  const farmerInputTotal =
    farmPlanItems
      ?.filter((item) => farmerInput.includes(item?.name))
      ?.reduce((acc, item) => acc + (item?.total_amount || 0), 0) || 0;
  const farmInputsContingency = farmerInputTotal + (farmerInputTotal * contingency) / 100;

  const formattedDate = currentDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <div
      className={`relative flex flex-col border bg-[url(/assets/forms/letter-of-authorization-to-hold-funds-form/letter-of-authorization-to-hold-funds-form-1.png)] bg-contain bg-top bg-no-repeat capitalize print:border-none ${FormDimensions.LETTER}`}
    >
      <div className="absolute top-[6.2rem] flex">
        {/* Date */}
        <div className="absolute left-36 top-3 w-[200px] text-sm">{formattedDate}</div>

        {/* Name */}
        <div className="absolute left-24 top-[38rem] w-[300px] uppercase">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>
      </div>
    </div>
  );
};

export default Page1;
