import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { businessInfoSchema, TBusinessInfoSchema } from '@/app/field-relation-officer/schemas';
import { IBusinessInfo, IFarmerBase, ISafeParseResult } from '@/app/field-relation-officer/types';
import useFarmer from '@/lib/hooks/fro/useFarmer';

import FarmBusinessInformation from './FarmBusinessInformation';
import FinancialInformation from './FinancialInformation';
import PurchaserInformation from './PurchaserInformation';

interface IBusinessInfoProps {
  data: IFarmerBase;
}

const BusinessInfo = ({ data: gStateFRO }: IBusinessInfoProps) => {
  const { updateFarmer } = useFarmer();
  const farmer = gStateFRO.farmer;
  const farmerInfo = farmer.farmerInfo;
  const {
    register,
    control,
    formState: { errors },
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TBusinessInfoSchema>({
    resolver: zodResolver(businessInfoSchema),
    defaultValues: {
      // Financial Information
      sourceOfFunds: farmer?.source_of_funds || '',
      monthlyGrossIncome: farmerInfo?.monthly_gross_income || 0,

      // Farm Business Information
      isMemberOfOrganization: farmerInfo?.is_member_of_organization?.toString() === '1' ? '1' : '0',
      organizationName: farmerInfo?.organization_name || '',
      organizationPosition: farmerInfo?.organization_position || '',
      hasPastFarmLoans: farmerInfo?.has_past_farm_loans?.toString() === '1' ? '1' : '0',
      pastFarmLoans: farmerInfo?.past_farm_loans || '',
      hasPastFarmLoanPaid: farmerInfo?.has_past_farm_loan_paid?.toString() === '1' ? '1' : '0',
      hasNeedFarmLoan: farmerInfo?.has_need_farm_loan?.toString() === '1' ? '1' : '0',
      needFarmLoanReason: farmerInfo?.need_farm_loan_reason || '',
      isInterestedToSellAtTradingPost:
        farmerInfo?.is_interested_to_sell_at_trading_post?.toString() === '1' ? '1' : '0',

      // Purchaser Information
      purchaserSellingLocation: farmerInfo?.purchaser_selling_location || '',
      purchaserFullname: farmerInfo?.purchaser_fullname || '',
      purchaserContactNumber: farmerInfo?.purchaser_contact_number || '',
    },
  });

  const handleNext = handleSubmit(async () => {
    const { data, success } = businessInfoSchema.safeParse(values()) as ISafeParseResult<IBusinessInfo>;

    if (success) {
      await updateFarmer(data);
      console.log('BusinessInfo:', data);
    }
  });

  return (
    <form onSubmit={handleNext}>
      <FinancialInformation register={register} control={control} errors={errors} />
      <FarmBusinessInformation register={register} control={control} errors={errors} watch={watch} />
      <PurchaserInformation register={register} control={control} errors={errors} watch={watch} />
      <div className="mt-16 flex justify-end gap-4">
        <Button type="submit">Submit</Button>
      </div>
    </form>
  );
};

export default BusinessInfo;
