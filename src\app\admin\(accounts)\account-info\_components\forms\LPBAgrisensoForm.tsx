'use client';

import { Check } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { FormDimensions } from '@/lib/constants/enums';
import { urlify } from '@/lib/utils';

export default function LbpAgrisensoForm({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
  farmPlan,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
  farmPlan: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'LBP Agrisenso Loan Form',
  });

  const address = farmerData && farmerData['farmer']['address'] ? JSON.parse(farmerData['farmer']['address']) : {};

  const tin =
    farmerData?.farmer?.governmentIdentifications?.find((id) => id?.government_id_type === 'TIN')
      ?.government_id_number || '';

  const formattedTin = tin ? tin.match(/.{1,3}/g)?.join('-') : '';

  const farmPlanItems = farmPlan[0]?.farmPlanItems?.get({ noproxy: true }) || [];
  const farmPlanCropName = farmPlan[0]?.crop?.name?.get({ noproxy: true }) || [];

  const [totalProductionCosts, setTotalProductionCosts] = useState(0);

  // farmer input
  const contingency = farmPlan[0]?.contingency_for_fluctuation?.get({ noproxy: true }) || 0;
  const farmerInput = [
    'Seed / Seedling Requirements (SE)',
    'Soil Fertilization - Basal (Top-Dress) (FE)',
    'Soil Fertilization - Additional (Side-Dress) (FE)',
    'Foliar Fertilization (Spray) (FE)',
    'Pesticide Application (Spray / Spread) (CP)',
    'Farm Materials, Consumables, etc.',
  ];
  const farmerInputTotal =
    farmPlanItems
      ?.filter((item) => farmerInput.includes(item?.name))
      ?.reduce((acc, item) => acc + (item?.total_amount || 0), 0) || 0;
  const farmInputsContingency = farmerInputTotal + (farmerInputTotal * contingency) / 100;

  // Cash Requirements
  const cashRequirements = ['Labor Requirements', 'Other Production Costs'];
  const cashRequirementsTotal =
    farmPlanItems
      ?.filter((item) => cashRequirements.includes(item?.name))
      ?.reduce((acc, item) => acc + (item?.total_amount || 0), 0) || 0;

  // Farmers Equity
  const farmersEquity = [
    'Non-Cash Costs',
    'KITA Subsidized Costs',
    'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)',
  ];
  const farmersEquityTotal =
    farmPlanItems
      .filter((items) => farmersEquity.includes(items?.name))
      ?.reduce((acc, item) => acc + (item?.total_amount || 0), 0) || 0;

  // Total Production Costs
  const calculateTotalProductionCosts = () => farmInputsContingency + cashRequirementsTotal + farmersEquityTotal;

  useEffect(() => {
    setTotalProductionCosts(calculateTotalProductionCosts());
  }, [farmInputsContingency, cashRequirementsTotal, farmersEquityTotal]);

  const formatCurrency = (value: number) =>
    value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>LBP Agrisenso Loan Form</DialogTitle>
          </DialogHeader>
          <div className="mx-auto flex max-h-[50vh] max-w-4xl flex-1 justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            {/* INPUT HERE */}
            <div ref={contentRef} className="font-poppins">
              <div
                className={`relative flex ${FormDimensions.LETTER} flex-col border bg-[url(/assets/forms/lbp-agrisenso-form.jpg)] bg-contain bg-top bg-no-repeat capitalize print:border-none`}
              >
                {/* Profile Image */}
                <img
                  className="absolute right-[60px] top-[80px] aspect-square h-[6.5rem] object-cover"
                  src={
                    farmerData?.user_img ? urlify(farmerData?.user_img, 'users/profile') : '/assets/user-default.jpg'
                  }
                  alt=""
                />

                {/* Name */}
                <div className="absolute left-[115px] top-[318px] text-xs font-medium capitalize">
                  {`${farmerData?.farmer?.first_name} ${farmerData?.farmer?.middle_name} ${farmerData?.farmer?.last_name}`}
                </div>

                {/* TIN */}
                <div className="absolute left-[390px] top-[318px] text-xs font-medium capitalize tracking-[1.2em]">
                  {formattedTin}
                </div>

                {/* Address */}
                <div className="absolute left-[45px] top-[347px] w-[310px]  text-xs font-medium capitalize leading-3">
                  {`${address?.addressHouseNumber}, ${address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}, ${address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ''}, ${address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''}`}
                </div>

                {/* Contact Number */}
                <div className="absolute left-[390px] top-[355px] text-xs font-medium capitalize">
                  {farmerData?.farmer?.mobile_number}
                </div>

                <div className="absolute left-[30px] top-[405px] text-xs font-medium capitalize">
                  <Check size={17} />
                </div>

                {/* Nature of Business */}
                <div className="absolute left-[115px] top-[530px] text-xs font-medium capitalize">Farming</div>

                {/* RSBSA */}
                <div className="absolute left-[220px] top-[565px] text-xs font-medium capitalize">
                  <Check size={17} />
                </div>
                <div className="absolute left-[380px] top-[565px] text-xs font-medium capitalize">
                  {farmerData?.farmer?.governmentIdentifications?.find((id) => id?.government_id_type === 'RSBSA')
                    ?.government_id_type || ''}
                </div>
                <div className="absolute left-[500px] top-[565px] text-xs font-medium capitalize">
                  {farmerData?.farmer?.governmentIdentifications?.find((id) => id?.government_id_type === 'RSBSA')
                    ?.government_id_number || ''}
                </div>

                {/* Financial Requirements */}
                <div className="absolute left-[30px] top-[655px] text-xs font-medium capitalize">
                  <Check size={17} />
                </div>

                <div className="absolute left-[450px] top-[660px] text-xs font-medium capitalize">
                  {formatCurrency(totalProductionCosts)}
                </div>
                <div className="absolute left-[575px] top-[655px] text-xs font-medium capitalize">
                  <Check size={17} />
                </div>

                {/* Crop */}
                <div className="absolute left-[80px] top-[800px] text-xs font-medium capitalize">
                  {farmPlanCropName}
                </div>

                {/* Existing Account at Landbank */}
                <div className={`absolute inset-x-8 top-[925px] h-20`}>
                  <div className="relative">
                    <Check
                      id="SA-ATM"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('SA-ATM') ? 'absolute top-5' : 'invisible'
                      }
                    />
                    <Check
                      id="TRUST"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('TRUST')
                          ? 'absolute left-[190px] top-5'
                          : 'invisible'
                      }
                    />
                    <Check
                      id="TRADE"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('TRADE')
                          ? 'absolute left-[435px] top-0.5'
                          : 'invisible'
                      }
                    />

                    <Check
                      id="CA-ATM"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('CA-ATM') ? 'absolute top-9' : 'invisible'
                      }
                    />
                    <Check
                      id="LOANS"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('LOANS') ? 'absolute top-0.5' : 'invisible'
                      }
                    />

                    <Check
                      id="TIME DEPOSIT"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('TIME DEPOSIT')
                          ? 'absolute left-[190px] top-0.5'
                          : 'invisible'
                      }
                    />
                    <Check
                      id="TREASURY"
                      size={17}
                      className={
                        farmerData?.farmer?.landbank_accounts?.includes('TREASURY')
                          ? 'absolute left-[190px] top-9'
                          : 'invisible'
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
