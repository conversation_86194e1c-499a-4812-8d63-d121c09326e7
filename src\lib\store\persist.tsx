'use client';

import { extend, hookstate, useHookstate } from '@hookstate/core';
// import { localstored } from '@hookstate/localstored';
import { devtools } from '@hookstate/devtools';
import merge from 'lodash.merge';

import { IBarangay, ICity, IProvince, IRegion } from '@/app/field-relation-officer/types';

import { DEFAULT_ADMIN_ACTIVE_MENU } from '../constants';
import { TabEWalletEnum, TabLoanHolderEnum } from '../constants/enums';
import { IAgronomistState, IFROPersist, ILogin, IOption } from './persist.types';
import { localstored } from './plugins/localStored';

const initialState = {
  user: null as ILogin,
  tabs: 'transactions',
  tabsAccounts: 'nonloan',
  tabsProductMgt: 'crops',
  tabsMarketplace: 'orders',
  tabsTradingApp: 'qa',
  tabsCreditScore: '1',
  tabsLoanHolder: 'loan-holder' as TabLoanHolderEnum.LOAN_HOLDER | TabLoanHolderEnum.LOAN_APPLICANTS,
  tabsEWallet: 'topup' as TabEWalletEnum.TOP_UP | TabEWalletEnum.TOP_DOWN,
  keep_me_logged_in: true,
  cred: {
    email: '',
    password: '',
  },
  selected: {
    from: '',
    account: {
      info: null as unknown,
    },
    farmer: null as any,
    currentLoan: null as any,
    loanReq: null as any,
    loanPayment: null as any,
    creditScoreGroup: null as any,
    creditScoreDetails: null as unknown,
    loanApplicationDetails: null as unknown,
  },
  admin: {
    activeMenu: DEFAULT_ADMIN_ACTIVE_MENU,
    members: {
      details: null as any,
    },
    transaction: {
      details: null as any,
    },
    orders: {
      customer: null as any,
      data: [],
      total: 0,
      shippingDate: '',
      fulfillmentType: '1',
    },
    viewOrder: null as any,
  },
  finance1: {
    reqDetails: null as any,
    topup: {
      farmer: null as any,
    },
  },
  finance: {
    accountInfo: {
      marketplaceDetails: null as any,
      financingDetails: null as any,
    },
    tabs: {
      marketplace: 'e-wallet',
    },
  },
  print: {
    farmerId: null as unknown,
  },
  sampleData: {
    products: [
      { itemName: 'Cabbage', price: '20', production_price: '10' },
      { itemName: 'Carrots', price: '25', production_price: '15' },
      { itemName: 'Tomato', price: '30', production_price: '20' },
    ],
  },
  agronomist: {
    activeMenu: 0,
    tabs: {
      farmPlanCalc: 'farm-plan',
    },
  } as IAgronomistState,
  fro: {
    activeMenu: 0,
    // NOTE: temporarily remove for testing
    // form: {
    //   activeStep: 1,
    //   step1: null,
    //   step2: null,
    //   step3: null,
    //   step4: null,
    // },
    edit: {
      activeTab: 'data-privacy',
    },
    options: {
      cropsPlanted: [],
      address: {
        regions: [] as IRegion[],
        provinces: [] as IProvince[],
        cities: [] as ICity[],
        barangays: [] as IBarangay[],
      },
    },
  } as IFROPersist,
};

export const globalStatePersist = hookstate(
  initialState,
  extend(
    localstored({
      key: 'globalStatePersist',
      onRestored: (s) => {
        const restored = s.get({ noproxy: true });

        if (s.value) {
          const synced = merge({}, initialState, restored);

          console.log('restored state: ', synced);
          s.set(synced);
        } else {
          console.log('restored state: localstorage is empty');
        }
      },
    }),
    devtools({ key: 'globalStatePersist' }),
  ),
);

export const useGlobalStatePersist = () => useHookstate(globalStatePersist);
