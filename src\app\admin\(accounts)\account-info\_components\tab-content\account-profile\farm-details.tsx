'use client';

import { useHookstate } from '@hookstate/core';
import { PaperclipIcon, Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useFarmer from '@/lib/hooks/useFarmer';
import usePublic from '@/lib/hooks/usePublic';
import useSelectAddress from '@/lib/hooks/utils/useSelectAddress';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';

const OPTION_LAND_CATEGORIES = [
  {
    label: 'IRRIGATED',
    value: 'IRRIGATED',
  },
  {
    label: 'RAIN FED',
    value: 'RAIN FED',
  },
  {
    label: 'IRRIGATED & RAIN FED',
    value: 'IRRIGATED & RAIN FED',
  },
];

export default function FarmDetails() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();

  // State for tracking "Others" option in Farm Ownership
  const [showOthersInput, setShowOthersInput] = useState(false);
  const {
    getAllCrops,
    getSeeds,
    getCropType,
    getFertilizer,
    getChemicals,
    OPTION_CROPTYPE,
    OPTION_CROPS,
    OPTION_CHEMICAL,
    OPTION_FERTILIZER,
    OPTION_CROPS_PLANTED,
  } = usePublic();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  // Initialize address hook for farm address
  const farmAddress = useSelectAddress();

  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors, isDirty },
  } = useForm({
    defaultValues: {
      // Segregated Farm Address fields
      farmAddressHouseNumber: data?.farmer?.farmerInfo?.farm_address_house_number?.value || '',
      farmAddressStreet: data?.farmer?.farmerInfo?.farm_address_street?.value || '',
      farmAddressRegion: data?.farmer?.farmerInfo?.farm_address_region?.value || '',
      farmAddressProvince: data?.farmer?.farmerInfo?.farm_address_province?.value || '',
      farmAddressCity: data?.farmer?.farmerInfo?.farm_address_city?.value || '',
      farmAddressBarangay: data?.farmer?.farmerInfo?.farm_address_barangay?.value || '',
      farmAddressZipCode: data?.farmer?.farmerInfo?.farm_address_zip_code?.value || '',
      farmArea: data?.farmer?.farmerInfo?.farm_area?.value || ('' as any),
      farmOwnership: `${data?.farmer?.farmerInfo?.farm_ownership?.value}` || '',
      otherFarmOwnership: data?.farmer?.farmerInfo?.other_farm_ownership?.value || '',
      priceBasedBy: data?.farmer?.farmerInfo?.price_based_by?.value || '',
      cropsPlanted:
        data?.farmer?.cropsPlanted?.value.map((v) => ({
          label: v.crop.name,
          value: `${v.crop.id}-${v.crop.name}`,
        })) || [],
      subCropsPlanted:
        data?.farmer?.subCropsPlanted?.value.map((v) => ({
          label: v.crop.name,
          value: `${v.crop.id}-${v.crop.name}`,
        })) || [],
      seedSubcategory:
        data?.farmer?.seedSubcategories?.value.map((v) => ({
          label: v.seedSubcategory.name,
          value: `${v.seedSubcategory.id}-${v.seedSubcategory.name}`,
        })) || [],
      fertilizer:
        data?.farmer?.fertilizers?.value.map((v) => ({
          label: v.fertilizer.name,
          value: `${v.fertilizer.id}-${v.fertilizer.name}`,
        })) || [],
      chemical:
        data?.farmer?.chemicals?.value.map((v) => ({
          label: v.chemical.name,
          value: `${v.chemical.id}-${v.chemical.name}`,
        })) || [],
      farmerVehicle:
        data?.farmer?.farmerVehicles?.value.map((v) => ({
          vehicleOwned: v.vehicle_owned,
          vehiclePlateNumber: v.vehicle_plate_number,
          orcr: '',
        })) || [],
      landCategory:
        data?.farmer?.farmerInsurance?.land_category?.value
          ?.split(',')
          .map((v) => ({ label: v.trim(), value: v.trim() })) || [],
      crop:
        data?.farmer?.farmerInsurance?.crop?.value?.split(',').map((v) => ({ label: v.trim(), value: v.trim() })) || [],
      phase: data?.farmer?.farmerInsurance?.phase?.value || '',
      ownerCultivator: data?.farmer?.farmerInsurance?.owner_cultivator?.value || '',
      tenant: data?.farmer?.farmerInsurance?.tenant?.value || '',
      cltEp: data?.farmer?.farmerInsurance?.clt_ep?.value || '',
      lessee: data?.farmer?.farmerInsurance?.lessee?.value || '',
      // Purchaser Information fields
      purchaserSellingLocation: data?.farmer?.farmerInfo?.purchaser_selling_location?.value || '',
      purchaserFullname: data?.farmer?.farmerInfo?.purchaser_fullname?.value || '',
      purchaserContactNumber: data?.farmer?.farmerInfo?.purchaser_contact_number?.value || '',
    },
  } as any);
  const farmerVehicle = useFieldArray({
    name: 'farmerVehicle',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      farmerVehicle: _data.farmerVehicle.map((s: any) => ({
        vehicleOwned: s.vehicleOwned,
        vehiclePlateNumber: s.vehiclePlateNumber,
      })),
      cropsPlanted: _data.cropsPlanted.map((item: any) => item.value.split('-')[0]),
      subCropsPlanted: _data.subCropsPlanted.map((item: any) => item.value.split('-')[0]),
      seedSubcategory: _data.seedSubcategory.map((item: any) => item.value.split('-')[0]),
      fertilizer: _data.fertilizer.map((item: any) => item.value.split('-')[0]),
      chemical: _data.chemical.map((item: any) => item.value.split('-')[0]),
      landCategory: _data.landCategory.map((item: any) => item.value).join(','),
      crop: _data.crop.map((item: any) => item.value).join(','),
      userId: data.farmer.user_id.value,
      // Include segregated farm address fields
      farmAddressHouseNumber: _data.farmAddressHouseNumber,
      farmAddressStreet: _data.farmAddressStreet,
      farmAddressRegion: _data.farmAddressRegion,
      farmAddressProvince: _data.farmAddressProvince,
      farmAddressCity: _data.farmAddressCity,
      farmAddressBarangay: _data.farmAddressBarangay,
      farmAddressZipCode: _data.farmAddressZipCode,
      // Include purchaser information fields
      purchaserSellingLocation: _data.purchaserSellingLocation,
      purchaserFullname: _data.purchaserFullname,
      purchaserContactNumber: _data.purchaserContactNumber,
      // Include farm ownership others field
      otherFarmOwnership: _data.otherFarmOwnership,
    };

    if (_data.farmOwnership === 'null') {
      delete updatedData.farmOwnership;
    }

    _data.farmerVehicle.map((s: any) => {
      if (s.orcr) {
        updatedData = {
          ...updatedData,
          [`farmerVehicle_vehicleOrcr_${s.vehiclePlateNumber}`]: s.orcr[0],
        };
      }
    });

    console.log('Farm Details: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  useEffect(() => {
    Promise.all([getAllCrops(), getSeeds(), getCropType(), getFertilizer(), getChemicals()]);
  }, []);

  // Initialize "Others" input visibility based on current farm ownership value
  useEffect(() => {
    const currentFarmOwnership = data?.farmer?.farmerInfo?.farm_ownership?.value;
    if (currentFarmOwnership === 3) {
      setShowOthersInput(true);
    }
  }, [data?.farmer?.farmerInfo?.farm_ownership?.value]);

  /**
   * ADDRESS INITIALIZATION
   * Helper functions and useEffect hooks to initialize address fields from saved data
   */

  // Helper function for safe JSON parsing
  const safeJsonParse = (jsonString: string | undefined, fieldName: string) => {
    if (!jsonString) return null;
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error(`Error parsing ${fieldName}:`, error);
      return null;
    }
  };

  // Helper function to initialize address fields
  const initializeAddressField = (
    list: any[],
    dataValue: string | undefined,
    codeKey: string,
    getterFn: (code: string) => any,
    setterFn: (item: any) => void,
    formFieldName: string,
    fieldName: string,
  ) => {
    if (list.length === 0 || !dataValue) return;

    const parsedData = safeJsonParse(dataValue, fieldName);
    if (!parsedData?.[codeKey]) return;

    const itemFromList = getterFn(parsedData[codeKey]);
    if (itemFromList) {
      setterFn(itemFromList);
      setValue(formFieldName, JSON.stringify(itemFromList));
    }
  };

  // Update Region
  useEffect(() => {
    if (farmAddress.regionList.length > 0) {
      const provinceData = safeJsonParse(
        data?.farmer?.farmerInfo?.farm_address_province?.value,
        'farm address province',
      );

      if (provinceData?.region_code) {
        const region = farmAddress.getRegionByCode(provinceData.region_code);
        if (region) {
          farmAddress.setRegionSelected(region);
          setValue('farmAddressRegion', JSON.stringify(region));
        }
      }
    }
  }, [farmAddress.regionList]);

  // Update Province and additional fields
  useEffect(() => {
    if (farmAddress.provinceList.length === 0) return;

    const farmerInfo = data.farmer.farmerInfo;
    const provinceData = safeJsonParse(farmerInfo?.farm_address_province?.value, 'farm address province');

    if (provinceData?.province_code) {
      const province = farmAddress.getProvinceByCode(provinceData.province_code);
      if (province) {
        farmAddress.setProvinceSelected(province);
        setValue('farmAddressProvince', JSON.stringify(province));

        // Set additional address fields
        const street = farmerInfo?.farm_address_street?.value || '';
        const houseNumber = farmerInfo?.farm_address_house_number?.value || '';
        const zipCode = farmerInfo?.farm_address_zip_code?.value || '';

        farmAddress.setStreet(street);
        farmAddress.setHouseNo(houseNumber);
        farmAddress.setPostalCode(zipCode);

        setValue('farmAddressStreet', street);
        setValue('farmAddressHouseNumber', houseNumber);
        setValue('farmAddressZipCode', String(zipCode));
      }
    }
  }, [farmAddress.provinceList]);

  // Update City
  useEffect(() => {
    if (farmAddress.cityList.length === 0) return;

    const cityData = safeJsonParse(data.farmer.farmerInfo?.farm_address_city?.value, 'farm address city');
    if (!cityData) return;

    // Handle region_desc to region_code conversion if needed
    if (cityData.region_desc) {
      cityData.region_code = cityData.region_desc;
      delete cityData.region_desc;
    }

    if (cityData.city_code) {
      const city = farmAddress.getCityByCode(cityData.city_code);
      if (city) {
        farmAddress.setCitySelected(city);
        setValue('farmAddressCity', JSON.stringify(cityData));
      }
    }
  }, [farmAddress.cityList]);

  // Update Barangay
  useEffect(() => {
    initializeAddressField(
      farmAddress.barangayList,
      data.farmer.farmerInfo?.farm_address_barangay?.value,
      'brgy_code',
      farmAddress.getBarangayByCode.bind(farmAddress),
      farmAddress.setBarangaySelected.bind(farmAddress),
      'farmAddressBarangay',
      'farm address barangay',
    );
  }, [farmAddress.barangayList]);

  return (
    <form id={PROFILE_TAB[5].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Farm Address</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* House Number */}
        <FormField name="farmAddressHouseNumber" label="House/Lot/Bldg No./Purok" errors={errors}>
          <Controller
            control={control}
            name="farmAddressHouseNumber"
            rules={{ required: false }}
            render={({ field: { onChange, value } }) => (
              <Input
                className={cn(
                  'focus-visible:ring-primary',
                  errors.farmAddressHouseNumber && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter House/Lot/Bldg No./Purok"
                value={value}
                onChange={(e) => {
                  onChange(e.target.value);
                  farmAddress.setHouseNo(e.target.value);
                }}
              />
            )}
          />
        </FormField>

        {/* Street */}
        <FormField name="farmAddressStreet" label="Street/Sitio/Subdivision" errors={errors}>
          <Controller
            control={control}
            name="farmAddressStreet"
            rules={{ required: false }}
            render={({ field: { onChange, value } }) => (
              <Input
                className={cn(
                  'focus-visible:ring-primary',
                  errors.farmAddressStreet && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Street/Sitio/Subdivision"
                value={value}
                onChange={(e) => {
                  onChange(e.target.value);
                  farmAddress.setStreet(e.target.value);
                }}
              />
            )}
          />
        </FormField>

        {/* Region */}
        <FormField key={JSON.stringify(farmAddress)} name="farmAddressRegion" label="Region" errors={errors}>
          <Controller
            control={control}
            name="farmAddressRegion"
            render={({ field: { onChange, value } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  if (v !== '') farmAddress.setRegionSelected(JSON.parse(v));
                }}
                value={value}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmAddressRegion && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {farmAddress.regionList.map((region: any) => (
                      <SelectItem key={region.region_code} value={JSON.stringify(region)}>
                        {region.region_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* Province */}
        <FormField name="farmAddressProvince" label="Province" errors={errors}>
          <Controller
            control={control}
            name="farmAddressProvince"
            render={({ field: { onChange, value } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  if (v !== '') farmAddress.setProvinceSelected(JSON.parse(v));
                }}
                value={value}
                disabled={farmAddress.provinceList.length === 0}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmAddressProvince && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select province" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {farmAddress.provinceList.map((province: any) => (
                      <SelectItem key={province.province_code} value={JSON.stringify(province)}>
                        {province.province_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* City/Municipality */}
        <FormField name="farmAddressCity" label="City/Municipality" errors={errors}>
          <Controller
            control={control}
            name="farmAddressCity"
            render={({ field: { onChange, value } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  if (v !== '') farmAddress.setCitySelected(JSON.parse(v));
                }}
                value={value}
                disabled={farmAddress.cityList.length === 0}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmAddressCity && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select city/municipality" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {farmAddress.cityList.map((city: any) => (
                      <SelectItem key={city.city_code} value={JSON.stringify(city)}>
                        {city.city_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* Barangay */}
        <FormField name="farmAddressBarangay" label="Barangay" errors={errors}>
          <Controller
            control={control}
            name="farmAddressBarangay"
            render={({ field: { onChange, value } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  if (v !== '') farmAddress.setBarangaySelected(JSON.parse(v));
                }}
                value={value}
                disabled={farmAddress.barangayList.length === 0}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmAddressBarangay && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Barangay" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {farmAddress.barangayList.map((barangay: any) => (
                      <SelectItem key={barangay.brgy_code} value={JSON.stringify(barangay)}>
                        {barangay.brgy_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* Zip Code */}
        <FormField name="farmAddressZipCode" label="Zip Code" errors={errors}>
          <Input
            {...register('farmAddressZipCode', {
              pattern: {
                value: /^\d{4}$/,
                message: 'Invalid Zip Code format (e.g. 1234)',
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.farmAddressZipCode && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Zip Code"
          />
        </FormField>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Farm Information</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Farm Area (hectare/s) */}
        <FormField name="farmArea" label="Farm Area (sqm)" errors={errors}>
          <Input
            {...register('farmArea', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Farm Area must be greater than 0' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.farmArea && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Farm Area (hectare/s)"
          />
        </FormField>

        {/* Farm Ownership */}
        <FormField name="farmOwnership" label="Farm Ownership" errors={errors}>
          <Controller
            control={control}
            name="farmOwnership"
            render={({ field: { onChange, value } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  setShowOthersInput(v === '3');
                  if (v !== '3') {
                    setValue('otherFarmOwnership', '');
                  }
                }}
                value={value}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmOwnership && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Farm Ownership" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="0">Owned (Sarili)</SelectItem>
                    <SelectItem value="1">Rented / Leased (Umuupa)</SelectItem>
                    <SelectItem value="3">Others</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* Farm Ownership Others - Conditional Input */}
        {showOthersInput && (
          <FormField name="otherFarmOwnership" label="Specify Others" errors={errors}>
            <Input
              {...register('otherFarmOwnership', {
                required: showOthersInput ? 'Please specify the farm ownership type' : false,
                maxLength: {
                  value: 100,
                  message: 'Farm ownership specification must not exceed 100 characters',
                },
                validate: {
                  notOnlySpaces: (v) =>
                    showOthersInput && v
                      ? (v as string).trim().length > 0 || 'Specification cannot be only spaces'
                      : true,
                },
              })}
              className={cn(
                'focus-visible:ring-primary',
                errors.otherFarmOwnership && 'border-red-500 focus-visible:ring-red-500',
              )}
              type="text"
              placeholder="Enter farm ownership type"
            />
          </FormField>
        )}

        {/* Price Determined By */}
        <FormField name="priceBasedBy" label="Price Determined By" errors={errors}>
          <Controller
            control={control}
            name="priceBasedBy"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.priceBasedBy && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Price Determined By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="BAPTC">BAPTC</SelectItem>
                    <SelectItem value="NVAT">NVAT</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>

      <div className="mt-8 font-dmSans text-xl font-bold text-primary">Vehicle Information</div>
      <div className="space-y-4 pr-14">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {farmerVehicle.fields.map((field, index) => {
            const errorForField = errors?.farmerVehicle?.[index];
            const isLast = data.farmer.farmerVehicles.length < index + 1;
            const orcImg = isLast ? '' : data.farmer.farmerVehicles[index].vehicle_orcr.value || '';
            const orcImgSplit = orcImg?.split('/');
            const orcName = orcImgSplit[orcImgSplit.length - 1];

            return (
              <div key={field.id} className="grid items-start gap-4 pb-3 pt-7 sm:grid-cols-2 xl:grid-cols-3">
                {/* Vehicle Plate No. */}
                <FormField
                  name={`farmerVehicle.${index}.vehiclePlateNumber`}
                  label="Vehicle Plate No."
                  errors={errors}
                  className="w-full max-w-sm"
                >
                  <Input
                    {...register(`farmerVehicle.${index}.vehiclePlateNumber` as const, {
                      required: false,
                      validate: {
                        isValidPlate: (v) => (v ? /^[A-Z0-9]{5,7}$/.test(v) || 'Invalid plate number' : true),
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vehiclePlateNumber && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Plate No."
                  />
                </FormField>

                {/* Vehicle Brand & Model */}
                <FormField
                  name={`farmerVehicle.${index}.vehicleOwned`}
                  label="Vehicle Brand & Model"
                  errors={errors}
                  className="w-full max-w-sm"
                >
                  <Input
                    {...register(`farmerVehicle.${index}.vehicleOwned` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vehicleOwned && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Vehicle Brand & Model"
                  />
                </FormField>

                {/* Upload ORCR */}
                <FormField
                  name={`farmerVehicle.${index}.orcr`}
                  label="Upload ORCR"
                  errors={errors}
                  className="w-full max-w-sm"
                >
                  <div className="relative">
                    <Input
                      {...register(`farmerVehicle.${index}.orcr` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.orcr && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="file"
                      placeholder="Enter Upload ORCR"
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => farmerVehicle.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>

                  {orcImg && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={orcImg} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {orcName}
                      </a>
                    </div>
                  )}
                </FormField>
              </div>
            );
          })}
        </div>

        {/* Add More Vehicle */}
        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              farmerVehicle.append({
                vehicleOwned: '',
                vehiclePlateNumber: '',
                orcr: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Vehicle</span>
          </Button>
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Crop Details</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Crops Planted */}
        {OPTION_CROPS_PLANTED.length > 0 && (
          <FormField name="cropsPlanted" label="Main Crops Planted" errors={errors}>
            <Controller
              control={control}
              name="cropsPlanted"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS_PLANTED}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Sub Crops Planted */}
        {OPTION_CROPS_PLANTED.length > 0 && (
          <FormField name="subCropsPlanted" label="Sub Crops Planted" errors={errors}>
            <Controller
              control={control}
              name="subCropsPlanted"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS_PLANTED}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Crop Type */}
        {OPTION_CROPTYPE.length > 0 && (
          <FormField name="seedSubcategory" label="Crop Type" errors={errors}>
            <Controller
              control={control}
              name="seedSubcategory"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPTYPE}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Fertilizers */}
        {OPTION_FERTILIZER.length > 0 && (
          <FormField name="fertilizer" label="Fertilizers" errors={errors}>
            <Controller
              control={control}
              name="fertilizer"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_FERTILIZER}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Chemicals */}
        {OPTION_CHEMICAL.length > 0 && (
          <FormField name="chemical" label="Chemicals" errors={errors}>
            <Controller
              control={control}
              name="chemical"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CHEMICAL}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                  groupBy="group"
                />
              )}
            />
          </FormField>
        )}
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Land Details</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Land Category */}
        <FormField name="landCategory" label="Land Category" errors={errors}>
          <Controller
            control={control}
            name="landCategory"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_LAND_CATEGORIES}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        {/* Crops */}
        {OPTION_CROPS.length > 0 && (
          <FormField name="crop" label="Crops" errors={errors}>
            <Controller
              control={control}
              name="crop"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS}
                  placeholder="Select from selection or create new"
                  creatable
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Phase */}
        <FormField name="phase" label="Phase" errors={errors}>
          <Input
            {...register('phase')}
            className={cn('focus-visible:ring-primary', errors.phase && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Phase"
          />
        </FormField>

        {/* Owner Cultivator */}
        <FormField name="ownerCultivator" label="Owner Cultivator" errors={errors}>
          <Input
            {...register('ownerCultivator')}
            className={cn(
              'focus-visible:ring-primary',
              errors.ownerCultivator && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Owner Cultivator"
          />
        </FormField>

        {/* Tenant */}
        <FormField name="tenant" label="Tenant" errors={errors}>
          <Input
            {...register('tenant')}
            className={cn('focus-visible:ring-primary', errors.tenant && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Tenant"
          />
        </FormField>

        {/* CLT/EP */}
        <FormField name="cltEp" label="CLT/EP" errors={errors}>
          <Input
            {...register('cltEp')}
            className={cn('focus-visible:ring-primary', errors.cltEp && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter CLT/EP"
          />
        </FormField>

        {/* Lessee */}
        <FormField name="lessee" label="Lessee" errors={errors}>
          <Input
            {...register('lessee')}
            className={cn('focus-visible:ring-primary', errors.lessee && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Lessee"
          />
        </FormField>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Purchaser Information</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Selling Location for Harvested Crops */}
        <FormField name="purchaserSellingLocation" label="Selling Location for Harvested Crops" errors={errors}>
          <Input
            {...register('purchaserSellingLocation', {
              required: false,
              maxLength: {
                value: 100,
                message: 'Selling Location must not exceed 100 characters',
              },
              validate: {
                notOnlySpaces: (v) =>
                  v ? (v as string).trim().length > 0 || 'Selling Location cannot be only spaces' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserSellingLocation && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Selling Location for Harvested Crops"
          />
        </FormField>

        {/* Buyer's Full Name */}
        <FormField name="purchaserFullname" label="Buyer's Full Name" errors={errors}>
          <Input
            {...register('purchaserFullname', {
              required: false,
              validate: {
                notOnlySpaces: (v) => (v ? (v as string).trim().length > 0 || 'Full name cannot be only spaces' : true),
                validName: (v) =>
                  v
                    ? /^[a-zA-Z\s\-\.\']+$/.test(v as string) ||
                      'Full name can only contain letters, spaces, hyphens, periods, and apostrophes'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserFullname && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Full Name"
          />
        </FormField>

        {/* Buyer's Contact No. */}
        <FormField name="purchaserContactNumber" label="Buyer's Contact No." errors={errors}>
          <Input
            {...register('purchaserContactNumber', {
              required: false,
              validate: {
                isValidContactNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid contact number format (e.g. 09123456789)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserContactNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Contact No."
          />
        </FormField>
      </div>
    </form>
  );
}
