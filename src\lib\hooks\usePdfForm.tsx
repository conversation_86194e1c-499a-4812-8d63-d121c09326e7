import { PDFDocument, StandardFonts } from 'pdf-lib';

import { toAllCaps } from '../utils';

export interface IKitaFormProps {
  surname: string;
  givenName: string;
  middleName: string;
  bday: string;
  birthPlace: string;
  gender: string;

  houseLot: string;
  street: string;
  barangay: string;
  city: string;
  province: string;
  zipCode: string;
  yearsResiding: number;
  residenceOwnership: string;
  occupationTitle: string;
  mobileNumber: string;
  governmentIdentifications: any[];
  email: string;
  civilStatus: string;
  familyProfiles: any[];
  farmArea: number;
  farmOwnership: number;
  skillsFarming: string;
  skillsOthers: string;
  cropsPlanted: any[];
  sourceOfFunds: string;
  annualIncome: string;
}

const extractBirthdayParts = (dateString: string) => {
  const [bdayYYYY, bdayMM, bdayDD] = dateString.split('-');
  return { bdayYYYY, bdayMM, bdayDD };
};

const ownershipMap: { [key: string]: string } = {
  OWNED: 'ownershipOwned',
  RENTED: 'ownershipRented',
  'LIVING WITH RELATIVES': 'ownershipWithRelatives',
};

const genderMap: { [key: string]: string } = {
  MALE: 'male',
  FEMALE: 'female',
};

const professionMap: { [key: string]: string } = {
  MALE: 'male',
  FEMALE: 'female',
};

const civilStatusMap: { [key: string]: string } = {
  SINGLE: 'single',
  MARRIED: 'married',
  WIDOWED: 'widowed',
  DIVORCED: 'divorced',
  SEPARATED: 'separated',
  LIVE_IN: 'liveIn',
};

const farmOwnershipMap: { [key: number]: string } = {
  0: 'landOwnershipOthers',
  1: 'landOwnershipOwned',
  2: 'landOwnershipRented',
};

const cropsPlantedMap: { [key: string]: string } = {
  PALAY: 'palayRice',
  CORN: 'maisCorn',
  VEGETABLE: 'vegetables',
};

const sourcesIncomeMap: { [key: string]: string } = {
  'FARM INCOME': 'farmIncome',
  'SALARY/HONORARIA': 'salaryHonoraria',
  'INTEREST/COMMISSION': 'interestCommission',
  BUSINESS: 'otherBusinessIncome',
  PENSION: '',
  'OFW REMITANCE': 'ofwRemittance',
  'OTHER REMITTANCE': 'otherRemittance',
};

// Helper function to safely parse JSON and extract property
export const safeJsonParse = (jsonString: string, property: string): string => {
  try {
    if (!jsonString || jsonString.trim() === '') {
      return '';
    }
    const parsed = JSON.parse(jsonString);
    return parsed[property] || '';
  } catch (error) {
    console.warn(`Failed to parse JSON for ${property}:`, jsonString);
    return jsonString; // Return the original string if parsing fails
  }
};

export const usePdfForm = () => {
  const downloadKitaForm = async (formData: IKitaFormProps) => {
    const {
      surname,
      givenName,
      middleName,
      birthPlace,
      bday,
      gender,
      houseLot,
      street,
      barangay,
      city,
      province,
      zipCode,
      yearsResiding,
      residenceOwnership,
      occupationTitle,
      mobileNumber,
      governmentIdentifications,
      email,
      civilStatus,
      familyProfiles,
      farmArea,
      farmOwnership,
      skillsFarming,
      skillsOthers,
      cropsPlanted,
      sourceOfFunds,
      annualIncome,
    } = formData;

    const { bdayYYYY, bdayMM, bdayDD } = extractBirthdayParts(bday);

    const formUrl = '/template/Registration_Form_Acroform.pdf';
    const existingPdfBytes = await fetch(formUrl).then((res) => res.arrayBuffer());

    const pdfDoc = await PDFDocument.load(existingPdfBytes);
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const form = pdfDoc.getForm();

    form.updateFieldAppearances(font);

    form.getTextField('surname').setText(toAllCaps(surname));
    form.getTextField('givenName').setText(toAllCaps(givenName));
    form.getTextField('middleName').setText(toAllCaps(middleName));
    form.getTextField('birthPlace').setText(toAllCaps(birthPlace));
    form.getTextField('middleName').setText(toAllCaps(middleName));
    form.getTextField('bdayMM').setText(bdayMM);
    form.getTextField('bdayDD').setText(bdayDD);
    form.getTextField('bdayYYYY').setText(bdayYYYY);
    form.getCheckBox('nationalityFilipino').check();
    form.getTextField('houseLot').setText(toAllCaps(houseLot));
    form.getTextField('street').setText(toAllCaps(street));

    form.getTextField('barangay').setText(toAllCaps(safeJsonParse(barangay, 'brgy_name')));
    form.getTextField('city').setText(toAllCaps(safeJsonParse(city, 'city_name')));
    form.getTextField('province').setText(toAllCaps(safeJsonParse(province, 'province_name')));
    form.getTextField('zipCode').setText(toAllCaps(zipCode));
    form.getTextField('yearsResiding').setText(toAllCaps(String(yearsResiding)));

    const genderField = genderMap[gender];
    if (genderField) {
      form.getCheckBox(genderField).check();
    }

    const ownershipField = ownershipMap[residenceOwnership];
    if (ownershipField) {
      form.getCheckBox(ownershipField).check();
    }

    const occupationField = professionMap[occupationTitle];
    if (occupationField) {
      form.getCheckBox(occupationField).check();
    }

    form.getTextField('mobileNo').setText(mobileNumber.substring(2));
    form.getTextField('telNo').setText('N/A');
    form.getTextField('fbName').setText('N/A');

    const govtIdList: string[] = [];
    for (const govID of governmentIdentifications) {
      if (govID.government_id_type === 'TIN') {
        form.getTextField('tin').setText(govID.government_id_number);
      } else if (govID.government_id_type === 'RSBSA') {
        for (let i = 1; i <= 15; i++) {
          const char = govID.government_id_number[i - 1] || '';
          form.getTextField(`rsbsa_n${i}`).setText(char);
        }
      } else {
        govtIdList.push(`ID: ${govID.government_id_type}, No.: ${govID.government_id_number}`);
      }
    }

    if (govtIdList.length > 0) {
      form.getTextField('govtId').setText(govtIdList.join(', '));
    }

    form.getTextField('email').setText(email);

    const civilStatusField = civilStatusMap[civilStatus];
    if (civilStatusField) {
      form.getCheckBox(civilStatusField).check();
    }

    if (civilStatus === 'MARRIED') {
      for (const family of familyProfiles) {
        if (family.relationship === 'SPOUSE') {
          form.getTextField('spouseGivenName').setText(toAllCaps(family.name.trim().split(/\s+/)[0]));
          form.getTextField('spouseLastName').setText(toAllCaps(family.name.trim().split(/\s+/)[1]));
          form.getTextField('spouseMobileNo').setText(mobileNumber.substring(2));
          // temporarily commented since this does not exist in current response
          // form.getTextField('spouseMobileNo').setText(family.mobileNumber.substring(2))
        }
      }
    }

    // professionOthers
    // form.getCheckBox('professionOthers').check();
    // form.getTextField('professionOthersDetails').setText('N/A');

    form.getTextField('farmCity').setText(toAllCaps(safeJsonParse(city, 'city_name')));
    form.getTextField('farmProvince').setText(toAllCaps(safeJsonParse(province, 'province_name')));
    form.getTextField('farmZipCode').setText(toAllCaps(zipCode));
    form.getTextField('farmArea').setText(String(farmArea));

    const farmOwnershipField = farmOwnershipMap[farmOwnership];
    if (farmOwnershipField) {
      form.getCheckBox(farmOwnershipField).check();
    }
    // temporarily commented since this does not exist in current response
    // form.getTextField('landOwnershipOthersDetails').setText(farmOwnershipOtherDetails);

    skillsFarming.split(',').map((crop) => {
      const cropsPlantedField = cropsPlantedMap[crop];
      if (cropsPlantedField) {
        form.getCheckBox(cropsPlantedField).check();
      }
    });

    if (skillsOthers) {
      form.getCheckBox('cropsOthers').check();
      form.getTextField('cropsOtherDetails').setText(toAllCaps(skillsOthers));
    }
    const cropsPlantedList: string[] = [];
    cropsPlanted.map((veg) => {
      cropsPlantedList.push(veg.crop.name);
    });
    form.getTextField('vegetablesDetails').setText(toAllCaps(cropsPlantedList.join(', ')));

    sourceOfFunds.split(',').map((crop) => {
      const sourcesIncomeField = sourcesIncomeMap[crop];
      if (sourcesIncomeField) {
        form.getCheckBox(sourcesIncomeField).check();
      }
    });

    form.getTextField('monthlyGrossIncome').setText(
      String(
        Number(Math.round((Number(annualIncome) / 12) * 100) / 100).toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
      ),
    );

    form.flatten();

    const pdfBytes = await pdfDoc.save();

    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${toAllCaps(givenName)} ${toAllCaps(surname)} (Kita Form).pdf`;
    document.body.appendChild(a);
    a.click();
    a.remove();
  };

  return { downloadKitaForm };
};
