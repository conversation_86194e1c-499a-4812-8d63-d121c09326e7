'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { PaperclipIcon, Plus, X } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';
import { cn, uniqueIndentifier } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';
import { FamilyRelationshipEnums, FarmerOccupationEnum } from './Enums';

export default function FamilyProfile() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      mothersMaidenName: data.farmer.mothers_maiden_name?.value || '',
      spouseName: data.farmer.spouse_name?.value || '',
      spouseMobileNumber: data.farmer.spouse_mobile_number?.value || '',
      familyProfile: data.farmer.familyProfiles.value.map((s) => ({
        name: s.name,
        relationship: s.relationship,
        birthDate: s.birth_date,
        occupation: s.occupation,
        occupationEmployerName: s.occupation_employer_name,
        occupationAnnualIncome: s.occupation_annual_income,
        isBarbazaMember: `${s.is_barbaza_member === null ? '' : s.is_barbaza_member}`,
        isBeneficiaries: `${s.is_beneficiaries === null ? '' : s.is_beneficiaries}`,
      })) || [
        {
          name: '',
          relationship: '',
          birthDate: '',
          occupation: '',
          occupationEmployerName: '',
          occupationAnnualIncome: '',
          isBarbazaMember: '',
          isBeneficiaries: '',
        },
      ],
      characterReference:
        data.farmer.farmerCharacterReferences.length > 0
          ? data.farmer.farmerCharacterReferences.value.map((s) => ({
              name: s.name,
              relationship: s.relationship,
              mobileNumber: s.mobile_number,
            }))
          : [
              {
                name: '',
                relationship: '',
                mobileNumber: '',
              },
            ],
      referrer:
        data.farmer.farmerReferrers.length > 0
          ? data.farmer.farmerReferrers.value.map((s) => ({
              referrerName: s.referrer_name,
              referrerMobileNumber: s.referrer_mobile_number,
              referrerPosition: s.referrer_position,
            }))
          : [
              {
                referrerName: '',
                referrerMobileNumber: '',
                referrerPosition: '',
                attachment: '',
              },
            ],
      vouchByLeader:
        data.farmer.farmerVouchLeaders.length > 0
          ? data.farmer.farmerVouchLeaders.value.map((s) => ({
              vouchByLeadersName: s.vouch_by_leaders_name,
              vouchByLeadersPosition: s.vouch_by_leaders_position,
              vouchByLeadersMobileNumber: s.vouch_by_leaders_mobile_number,
            }))
          : [
              {
                vouchByLeadersName: '',
                vouchByLeadersPosition: '',
                vouchByLeadersMobileNumber: '',
                attachment: '',
              },
            ],
      vouchByMao:
        data.farmer.farmerVouchMaos.length > 0
          ? data.farmer.farmerVouchMaos.value.map((s) => ({
              vouchByMaosName: s.vouch_by_maos_name,
              vouchByMaosPosition: s.vouch_by_maos_position,
              vouchByMaosMobileNumber: s.vouch_by_maos_mobile_number,
            }))
          : [
              {
                vouchByMaosName: '',
                vouchByMaosPosition: '',
                vouchByMaosMobileNumber: '',
                attachment: '',
              },
            ],
    },
  });
  const familyProfile = useFieldArray({
    name: 'familyProfile',
    control,
  });
  const characterReference = useFieldArray({
    name: 'characterReference',
    control,
  });
  const referrer = useFieldArray({
    name: 'referrer',
    control,
  });
  const vouchByLeader = useFieldArray({
    name: 'vouchByLeader',
    control,
  });
  const vouchByMao = useFieldArray({
    name: 'vouchByMao',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      vouchByLeader: _data.vouchByLeader.map((s) => {
        const find = data.farmer.farmerVouchLeaders.value.find((v) => v.vouch_by_leaders_name === s.vouchByLeadersName);
        const identifier = find ? find.identifier : uniqueIndentifier();

        return {
          vouchByLeadersName: s.vouchByLeadersName,
          vouchByLeadersPosition: s.vouchByLeadersPosition,
          vouchByLeadersMobileNumber: s.vouchByLeadersMobileNumber,
          identifier,
        };
      }),
      vouchByMao: _data.vouchByMao.map((s) => {
        const find = data.farmer.farmerVouchMaos.value.find((v) => v.vouch_by_maos_name === s.vouchByMaosName);
        const identifier = find ? find.identifier : uniqueIndentifier();

        return {
          vouchByMaosName: s.vouchByMaosName,
          vouchByMaosPosition: s.vouchByMaosPosition,
          vouchByMaosMobileNumber: s.vouchByMaosMobileNumber,
          identifier,
        };
      }),
      referrer: _data.referrer.map((s) => {
        const find = data.farmer.farmerReferrers.value.find((v) => v.referrer_name === s.referrerName);
        const identifier = find ? find.identifier : uniqueIndentifier();

        return {
          referrerName: s.referrerName,
          referrerMobileNumber: s.referrerMobileNumber,
          referrerPosition: s.referrerPosition,
          identifier,
        };
      }),
      userId: data.farmer.user_id.value,
    };

    _data.vouchByLeader.map((item, index) => {
      updatedData = {
        ...updatedData,
        [`vouchByLeader_${updatedData.vouchByLeader[index].identifier}`]:
          item.attachment.length > 0 ? item.attachment[0] : null,
      };
    });

    _data.vouchByMao.map((item, index) => {
      updatedData = {
        ...updatedData,
        [`vouchByMao_${updatedData.vouchByMao[index].identifier}`]:
          item.attachment.length > 0 ? item.attachment[0] : null,
      };
    });

    _data.referrer.map((item, index) => {
      updatedData = {
        ...updatedData,
        [`referrer_${updatedData.referrer[index].identifier}`]: item.attachment.length > 0 ? item.attachment[0] : null,
      };
    });

    if (updatedData.characterReference.length === 1) {
      if (!updatedData.characterReference[0].name) {
        delete updatedData.characterReference;
      }
    }

    if (updatedData.vouchByLeader.length === 1) {
      if (!updatedData.vouchByLeader[0].vouchByLeadersName) {
        delete updatedData.vouchByLeader;
      }
    }

    if (updatedData.vouchByMao.length === 1) {
      if (!updatedData.vouchByMao[0].vouchByMaosName) {
        delete updatedData.vouchByMao;
      }
    }

    if (updatedData.referrer.length === 1) {
      if (!updatedData.referrer[0].referrerName) {
        delete updatedData.referrer;
      }
    }

    console.log('Family Profile: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  return (
    <form id={PROFILE_TAB[3].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-4 pr-6">
        <div className="space-y-4 divide-y-2 divide-dashed pr-10">
          {familyProfile.fields.map((field, index) => {
            const errorForField = errors?.familyProfile?.[index];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.name`} className="pb-1 font-normal">
                    First, Middle, Last Name
                  </Label>
                  <Input
                    {...register(`familyProfile.${index}.name` as const, {
                      required: false,
                      // validate: {
                      //   isWrongPattern: (v) =>
                      //     v ? /^(\w+(\s\w+)?)?\s?\w+$/.test(v) || 'Invalid First Middle Last Name' : true,
                      // },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.name && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter First, Middle, Last Name"
                  />
                  {errorForField?.name && <p className="form-error">{`${errorForField?.name?.message}`}</p>}
                </div>

                {/* Relationship */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.relationship`} className="pb-1 font-normal">
                    Relationship
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.relationship` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.relationship &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Relationship" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(FamilyRelationshipEnums).map((familyRelationship) => (
                              <SelectItem key={familyRelationship} value={familyRelationship}>
                                {familyRelationship}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.relationship && (
                    <p className="form-error">{`${errorForField?.relationship?.message}`}</p>
                  )}
                </div>

                {/* Birthdate */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.birthDate`} className="pb-1 font-normal">
                    Birth Date
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`familyProfile.${index}.birthDate` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.birthDate && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="date"
                      placeholder="Enter Birthdate"
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => familyProfile.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.birthDate && <p className="form-error">{`${errorForField?.birthDate?.message}`}</p>}
                </div>

                {/* Occupation */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.occupation`} className="pb-1 font-normal">
                    Occupation
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.occupation` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.occupation &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Occupation" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(FarmerOccupationEnum).map((occu) => (
                              <SelectItem key={occu} value={occu}>
                                {occu}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.occupation && <p className="form-error">{`${errorForField?.occupation?.message}`}</p>}
                </div>

                {/* Employer Name / School Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.occupationEmployerName`} className="pb-1 font-normal">
                    {watch(`familyProfile.${index}.occupation`) === 'STUDENT' ? 'Name of School' : 'Employer Name'}
                  </Label>
                  <Input
                    {...register(`familyProfile.${index}.occupationEmployerName` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.occupationEmployerName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder={
                      watch(`familyProfile.${index}.occupation`) === 'STUDENT'
                        ? 'Enter School Name'
                        : 'Enter Employer Name'
                    }
                  />
                  {errorForField?.occupationEmployerName && (
                    <p className="form-error">{`${errorForField?.occupationEmployerName?.message}`}</p>
                  )}
                </div>

                {/* Annual Income */}
                {watch(`familyProfile.${index}.occupation`) !== 'STUDENT' && (
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`familyProfile.${index}.occupationAnnualIncome`} className="pb-1 font-normal">
                      Annual Income
                    </Label>
                    <Input
                      {...register(`familyProfile.${index}.occupationAnnualIncome` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.occupationAnnualIncome && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Annual Income"
                    />
                    {errorForField?.occupationAnnualIncome && (
                      <p className="form-error">{`${errorForField?.occupationAnnualIncome?.message}`}</p>
                    )}
                  </div>
                )}

                {/* Barbaza Member */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.isBarbazaMember`} className="pb-1 font-normal">
                    Barbaza MPC Member
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.isBarbazaMember` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.isBarbazaMember &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Barbaza Member?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="0">NO</SelectItem>
                            <SelectItem value="1">YES</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.isBarbazaMember && (
                    <p className="form-error">{`${errorForField?.isBarbazaMember?.message}`}</p>
                  )}
                </div>

                {/* Beneficiary */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.isBeneficiaries`} className="pb-1 font-normal">
                    Add to your Beneficiaries
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.isBeneficiaries` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.isBeneficiaries &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Add to your beneficiaries?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="0">NO</SelectItem>
                            <SelectItem value="1">YES</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.isBeneficiaries && (
                    <p className="form-error">{`${errorForField?.isBeneficiaries?.message}`}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              familyProfile.append({
                name: '',
                relationship: '',
                birthDate: '',
                occupation: '',
                occupationEmployerName: '',
                occupationAnnualIncome: '',
                isBarbazaMember: '',
                isBeneficiaries: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Family Member</span>
          </Button>
        </div>

        {/* Spouse Information - Only show if married */}
        {data.farmer.civil_status?.value === 'MARRIED' && (
          <div className="mt-6 space-y-4">
            <div className="font-dmSans text-xl font-bold text-primary">Spouse Information</div>
            <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2">
              {/* Spouse Name */}
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="spouseName" className="pb-1 font-normal">
                  Spouse Name (First, Middle, Last Name) <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('spouseName', {
                    required: data.farmer.civil_status?.value === 'MARRIED' ? 'Spouse name is required' : false,
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.spouseName && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Spouse Name"
                />
                {errors.spouseName && <p className="form-error">{`${errors.spouseName.message}`}</p>}
              </div>

              {/* Spouse Mobile Number */}
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="spouseMobileNumber" className="pb-1 font-normal">
                  Spouse Mobile No. <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('spouseMobileNumber', {
                    required:
                      data.farmer.civil_status?.value === 'MARRIED' ? 'Spouse mobile number is required' : false,
                    validate: {
                      isValidMobileNumber: (v) =>
                        v && data.farmer.civil_status?.value === 'MARRIED'
                          ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                            'Invalid mobile number format (e.g. 09123456789)'
                          : true,
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.spouseMobileNumber && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Spouse Mobile No."
                />
                {errors.spouseMobileNumber && <p className="form-error">{`${errors.spouseMobileNumber.message}`}</p>}
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 font-dmSans text-xl font-bold text-primary">Character References</div>
        <div className="space-y-4 divide-y-2 divide-dashed pr-10">
          {characterReference.fields.map((field, index) => {
            const errorForField = errors?.characterReference?.[index];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`characterReference.${index}.name`} className="pb-1 font-normal">
                    First, Middle, Last Name
                  </Label>
                  <Input
                    {...register(`characterReference.${index}.name` as const, {
                      required: false,
                      // validate: {
                      //   isWrongPattern: (v) =>
                      //     v ? /^(\w+(\s\w+)?)?\s?\w+$/.test(v) || 'Invalid First Middle Last Name' : true,
                      // },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.name && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter First, Middle, Last Name"
                  />
                  {errorForField?.name && <p className="form-error">{`${errorForField?.name?.message}`}</p>}
                </div>

                {/* Relationship */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`characterReference.${index}.relationship`} className="pb-1 font-normal">
                    Relationship
                  </Label>
                  <Input
                    {...register(`characterReference.${index}.relationship` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.relationship && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Relationship"
                  />
                  {errorForField?.relationship && (
                    <p className="form-error">{`${errorForField?.relationship?.message}`}</p>
                  )}
                </div>

                {/* Mobile Number */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`characterReference.${index}.mobileNumber`} className="pb-1 font-normal">
                    Mobile No.
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`characterReference.${index}.mobileNumber` as const, {
                        required: false,
                        validate: {
                          isValidMobileNumber: (v) =>
                            v
                              ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                                'Invalid mobile number format (e.g. 09123456789)'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.mobileNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Mobile No."
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => characterReference.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.mobileNumber && (
                    <p className="form-error">{`${errorForField?.mobileNumber.message}`}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              characterReference.append({
                name: '',
                relationship: '',
                mobileNumber: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Character Reference</span>
          </Button>
        </div>

        <div className="mt-6 font-dmSans text-xl font-bold text-primary">Vouch by Cooperative Leader</div>
        <div className="space-y-4 divide-y-2 divide-dashed pr-10">
          {vouchByLeader.fields.map((field, index) => {
            const errorForField = errors?.vouchByLeader?.[index];

            const find = data.farmer.farmerVouchLeaders
              .get({ noproxy: true })
              .find((v) => v.vouch_by_leaders_name === field['vouchByLeadersName']);
            console.log('find: ', find);
            const image = find?.vouch_by_leaders_attachment || '';
            const govImgSplit = image?.split('/');
            const govName = govImgSplit[govImgSplit.length - 1];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByLeader.${index}.vouchByLeadersName`} className="pb-1 font-normal">
                    First, Middle, Last Name
                  </Label>
                  <Input
                    {...register(`vouchByLeader.${index}.vouchByLeadersName` as const, {
                      required: false,
                      // validate: {
                      //   isWrongPattern: (v) =>
                      //     v ? /^(\w+(\s\w+)?)?\s?\w+$/.test(v) || 'Invalid First Middle Last Name' : true,
                      // },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vouchByLeadersName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter First, Middle, Last Name"
                  />
                  {errorForField?.vouchByLeadersName && (
                    <p className="form-error">{`${errorForField?.vouchByLeadersName?.message}`}</p>
                  )}
                </div>

                {/* Position */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByLeader.${index}.vouchByLeadersPosition`} className="pb-1 font-normal">
                    Position
                  </Label>
                  <Input
                    {...register(`vouchByLeader.${index}.vouchByLeadersPosition` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vouchByLeadersPosition && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Position"
                  />
                  {errorForField?.vouchByLeadersPosition && (
                    <p className="form-error">{`${errorForField?.vouchByLeadersPosition?.message}`}</p>
                  )}
                </div>

                {/* Mobile No. */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByLeader.${index}.vouchByLeadersMobileNumber`} className="pb-1 font-normal">
                    Mobile No.
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`vouchByLeader.${index}.vouchByLeadersMobileNumber` as const, {
                        required: false,
                        validate: {
                          isValidMobileNumber: (v) =>
                            v
                              ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                                'Invalid mobile number format (e.g. 09123456789)'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.vouchByLeadersMobileNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Mobile No."
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => vouchByLeader.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.vouchByLeadersMobileNumber && (
                    <p className="form-error">{`${errorForField?.vouchByLeadersMobileNumber.message}`}</p>
                  )}
                </div>

                {/* Attachment */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByLeader.${index}.attachment`} className="pb-1 font-normal">
                    Attachment
                  </Label>
                  <Input
                    {...register(`vouchByLeader.${index}.attachment` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.attachment && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="file"
                    placeholder="No file selected"
                  />
                  {errorForField?.attachment && <p className="form-error">{`${errorForField?.attachment?.message}`}</p>}

                  {image && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={image} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {govName}
                      </a>
                    </div>
                  )}

                  {find && (
                    <div className="relative h-4 text-sm text-gray-500">
                      <div className="absolute left-0 top-2 min-w-max">
                        Date & time updated: {format(new Date(find.updated_at), 'MMM dd, yyyy, hh:mm a')}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              vouchByLeader.append({
                vouchByLeadersName: '',
                vouchByLeadersPosition: '',
                vouchByLeadersMobileNumber: '',
                attachment: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More</span>
          </Button>
        </div>

        <div className="mt-6 font-dmSans text-xl font-bold text-primary">Referrer</div>
        <div className="space-y-4 divide-y-2 divide-dashed pr-10">
          {referrer.fields.map((field, index) => {
            const errorForField = errors?.referrer?.[index];

            const find = data.farmer.farmerReferrers
              .get({ noproxy: true })
              .find((v) => v.referrer_name === field['referrerName']);
            console.log('find: ', find);
            const image = find?.referrer_attachment || '';
            const govImgSplit = image?.split('/');
            const govName = govImgSplit[govImgSplit.length - 1];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`referrer.${index}.referrerName`} className="pb-1 font-normal">
                    First, Middle, Last Name
                  </Label>
                  <Input
                    {...register(`referrer.${index}.referrerName` as const, {
                      required: false,
                      // validate: {
                      //   isWrongPattern: (v) =>
                      //     v ? /^(\w+(\s\w+)?)?\s?\w+$/.test(v) || 'Invalid First Middle Last Name' : true,
                      // },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.referrerName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter First, Middle, Last Name"
                  />
                  {errorForField?.referrerName && (
                    <p className="form-error">{`${errorForField?.referrerName?.message}`}</p>
                  )}
                </div>

                {/* Position */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`referrer.${index}.referrerPosition`} className="pb-1 font-normal">
                    Position
                  </Label>
                  <Input
                    {...register(`referrer.${index}.referrerPosition` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.referrerPosition && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Position"
                  />
                  {errorForField?.referrerPosition && (
                    <p className="form-error">{`${errorForField?.referrerPosition?.message}`}</p>
                  )}
                </div>

                {/* Mobile No. */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`referrer.${index}.referrerMobileNumber`} className="pb-1 font-normal">
                    Mobile No.
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`referrer.${index}.referrerMobileNumber` as const, {
                        required: false,
                        validate: {
                          isValidMobileNumber: (v) =>
                            v
                              ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                                'Invalid mobile number format (e.g. 09123456789)'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.referrerMobileNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Mobile No."
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => referrer.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.referrerMobileNumber && (
                    <p className="form-error">{`${errorForField?.referrerMobileNumber.message}`}</p>
                  )}
                </div>

                {/* Attachment */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`referrer.${index}.attachment`} className="pb-1 font-normal">
                    Attachment
                  </Label>
                  <Input
                    {...register(`referrer.${index}.attachment` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.attachment && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="file"
                    placeholder="No file selected"
                  />
                  {errorForField?.attachment && <p className="form-error">{`${errorForField?.attachment?.message}`}</p>}

                  {image && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={image} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {govName}
                      </a>
                    </div>
                  )}

                  {find && (
                    <div className="relative h-4 text-sm text-gray-500">
                      <div className="absolute left-0 top-2 min-w-max">
                        Date & time updated: {format(new Date(find.updated_at), 'MMM dd, yyyy, hh:mm a')}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              referrer.append({
                referrerName: '',
                referrerPosition: '',
                referrerMobileNumber: '',
                attachment: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More</span>
          </Button>
        </div>

        <div className="mt-6 font-dmSans text-xl font-bold text-primary">
          Vouch by Municipal Agriculture Office (MAO)
        </div>
        <div className="space-y-4 divide-y-2 divide-dashed pr-10">
          {vouchByMao.fields.map((field, index) => {
            const errorForField = errors?.vouchByMao?.[index];

            const find = data.farmer.farmerVouchMaos
              .get({ noproxy: true })
              .find((v) => v.vouch_by_maos_name === field['vouchByMaosName']);
            console.log('find: ', find);
            const image = find?.vouch_by_maos_attachment || '';
            const govImgSplit = image?.split('/');
            const govName = govImgSplit[govImgSplit.length - 1];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByMao.${index}.vouchByMaosName`} className="pb-1 font-normal">
                    First, Middle, Last Name
                  </Label>
                  <Input
                    {...register(`vouchByMao.${index}.vouchByMaosName` as const, {
                      required: false,
                      // validate: {
                      //   isWrongPattern: (v) =>
                      //     v ? /^(\w+(\s\w+)?)?\s?\w+$/.test(v) || 'Invalid First Middle Last Name' : true,
                      // },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vouchByMaosName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter First, Middle, Last Name"
                  />
                  {errorForField?.vouchByMaosName && (
                    <p className="form-error">{`${errorForField?.vouchByMaosName?.message}`}</p>
                  )}
                </div>

                {/* Position */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByMao.${index}.vouchByMaosPosition`} className="pb-1 font-normal">
                    Position
                  </Label>
                  <Input
                    {...register(`vouchByMao.${index}.vouchByMaosPosition` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vouchByMaosPosition && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Position"
                  />
                  {errorForField?.vouchByMaosPosition && (
                    <p className="form-error">{`${errorForField?.vouchByMaosPosition?.message}`}</p>
                  )}
                </div>

                {/* Mobile No. */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByMao.${index}.vouchByMaosMobileNumber`} className="pb-1 font-normal">
                    Mobile No.
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`vouchByMao.${index}.vouchByMaosMobileNumber` as const, {
                        required: false,
                        validate: {
                          isValidMobileNumber: (v) =>
                            v
                              ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                                'Invalid mobile number format (e.g. 09123456789)'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.vouchByMaosMobileNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Mobile No."
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => vouchByMao.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.vouchByMaosMobileNumber && (
                    <p className="form-error">{`${errorForField?.vouchByMaosMobileNumber.message}`}</p>
                  )}
                </div>

                {/* Attachment */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`vouchByMao.${index}.attachment`} className="pb-1 font-normal">
                    Attachment
                  </Label>
                  <Input
                    {...register(`vouchByMao.${index}.attachment` as const, {
                      required: false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.attachment && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="file"
                    placeholder="No file selected"
                  />
                  {errorForField?.attachment && <p className="form-error">{`${errorForField?.attachment?.message}`}</p>}

                  {image && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={image} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {govName}
                      </a>
                    </div>
                  )}

                  {find && (
                    <div className="relative h-4 text-sm text-gray-500">
                      <div className="absolute left-0 top-2 min-w-max">
                        Date & time updated: {format(new Date(find.updated_at), 'MMM dd, yyyy, hh:mm a')}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              vouchByMao.append({
                vouchByMaosName: '',
                vouchByMaosPosition: '',
                vouchByMaosMobileNumber: '',
                attachment: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More</span>
          </Button>
        </div>
      </div>
    </form>
  );
}
