import { Check } from 'lucide-react';
import React from 'react';

import { FARM_IMPLEMENTS, WATER_SOURCE } from '@/app/field-relation-officer/constants';
import { FormDimensions } from '@/lib/constants/enums';
import { safeJsonParse } from '@/lib/hooks/usePdfForm';
import { urlify } from '@/lib/utils';

const extractBirthdayParts = (dateString: string) => {
  const [bdayYYYY, bdayMM, bdayDD] = (dateString || '')?.split('-');
  return { bdayYYYY, bdayMM, bdayDD };
};

const cropsPlantedMap: { [key: string]: string } = {
  PALAY: 'palayRice',
  CORN: 'maisCorn',
  VEGETABLE: 'vegetables',
};

const Page1 = ({ data, farmerProfileImg, email }) => {
  const bday = data?.birth_date;

  const { bdayYYYY, bdayMM, bdayDD } = extractBirthdayParts(bday);

  const govtIdList: string[] = [];
  let tin = '';
  let rsbsa1 = '';
  let rsbsa2 = '';
  let rsbsa3 = '';
  let rsbsa4 = '';
  let rsbsa5 = '';
  for (const govID of data?.governmentIdentifications) {
    if (govID.government_id_type === 'TIN') {
      tin = govID.government_id_number ? govID.government_id_number.match(/.{1,3}/g)?.join('-') : '';
    } else if (govID.government_id_type === 'RSBSA') {
      rsbsa1 = govID.government_id_number.slice(0, 2);
      rsbsa2 = govID.government_id_number.slice(2, 4);
      rsbsa3 = govID.government_id_number.slice(4, 6);
      rsbsa4 = govID.government_id_number.slice(6, 9);
      rsbsa5 = govID.government_id_number.slice(9, 15);
    } else {
      govtIdList.push(`ID: ${govID.government_id_type}, No.: ${govID.government_id_number}`);
    }
  }

  let spouse = '';
  for (const profile of data?.familyProfiles) {
    if (profile.relationship === 'SPOUSE') {
      spouse = profile.name;
    }
  }

  let palayRice = false;
  let corn = false;
  let vegetable = false;
  (data?.skills_farming || '').split(',').map((crop) => {
    const cropsPlantedField = cropsPlantedMap[crop];
    if (cropsPlantedField === cropsPlantedMap.PALAY) {
      palayRice = true;
    }
    if (cropsPlantedField === cropsPlantedMap.CORN) {
      corn = true;
    }
    if (cropsPlantedField === cropsPlantedMap.VEGETABLE) {
      vegetable = true;
    }
  });

  console.log('DATA', data);
  console.log('KITA FARMER INFO', data?.farmerInfo);

  const KNOWN_WATER_SOURCES = WATER_SOURCE.map(({ value }) => value);
  const waterSourceValue = data?.farmerInfo?.water_source || '';
  const hasOtherWaterSourceValue = waterSourceValue?.split(',').map((val) => val.trim());

  const KNOWN_FARM_IMPLEMENTS = FARM_IMPLEMENTS.map(({ value }) => value);
  const farmImplements = data?.farmerInfo?.farm_implements || '';
  const hasOtherFarmImplements = farmImplements.split(',').map((val) => val.trim());

  return (
    <div
      className={`relative flex ${FormDimensions.LEGAL} flex-col border bg-[url(/assets/forms/kita-form/kita-form-1.jpg)] bg-contain bg-top bg-no-repeat capitalize print:border-none`}
    >
      {/* Profile Image */}
      <img
        className="absolute right-[25px] top-[12.1rem] aspect-square h-[11.9rem] object-cover"
        src={farmerProfileImg ? urlify(farmerProfileImg, 'users/profile') : '/assets/user-default.jpg'}
        alt=""
      />

      {/* name */}
      <div className="absolute left-[12.5rem] right-4 top-[12.5rem] text-xs uppercase">{data?.first_name || ' '}</div>
      <div className="absolute left-[12.5rem] right-4 top-[14.1rem] text-xs uppercase">{data?.middle_name || ' '}</div>
      <div className="absolute left-[12.5rem] right-4 top-[15.6rem] text-xs uppercase">{data?.last_name || ' '}</div>

      <div className="absolute top-[17.3rem] flex">
        {/* birthdate */}
        <div className="absolute left-[2.6rem] top-1 flex flex-1 text-xs tracking-[1em]">{bdayMM}</div>
        <div className="absolute left-[6.5rem] top-1 flex flex-1 text-xs tracking-[1em]">{bdayDD}</div>
        <div className="absolute left-44 top-1 flex flex-1 text-xs tracking-[1em]">{bdayYYYY}</div>

        {/* Birthplace */}
        <div className="absolute left-72 top-1 w-[300px]  text-center text-xs uppercase">
          {data?.place_of_birth || ''}
        </div>

        {/* Sex Male */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.gender === 'MALE' ? 'left-[14.8rem] top-[2.7rem]' : 'hidden'}`}
        />
        {/* Sex Female */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.gender === 'FEMALE' ? 'left-[9.8rem] top-[2.7rem]' : 'hidden'}`}
        />
        {/* Nationality */}
        <Check strokeWidth={5} className="absolute left-[27.1rem] top-[2.7rem] flex text-sm" />

        {/* RSBSA */}
        <div className="absolute left-[11.6rem] top-[4.9rem] tracking-[0.7em]">{rsbsa1}</div>
        <div className="absolute left-[15.7rem] top-[4.9rem] tracking-[0.7em]">{rsbsa2}</div>
        <div className="absolute left-[19.7rem] top-[4.9rem] tracking-[0.7em]">{rsbsa3}</div>
        <div className="absolute left-[23.8rem] top-[4.9rem] tracking-[0.8em]">{rsbsa4}</div>
        <div className="absolute left-[29.4rem] top-[4.9rem] tracking-[0.7em]">{rsbsa5}</div>

        {/* Address */}
        <div className="absolute left-48 top-[7.6rem] w-44 text-center text-xs uppercase">
          {data?.address_house_number}
        </div>

        {/* Street */}
        <div className="absolute left-96 top-[7.6rem] w-48 text-center text-xs uppercase">{data?.address_street}</div>

        {/* Barangay */}
        <div className="absolute left-[36.8rem] top-[7.6rem] w-44  text-center text-xs uppercase">
          {safeJsonParse(data?.address_barangay, 'brgy_name')}
        </div>

        {/* City */}
        <div className="absolute left-8 top-[10.7rem] w-60 text-center text-xs uppercase">
          {safeJsonParse(data?.address_city, 'city_name')}
        </div>

        {/* Province */}
        <div className="absolute left-[18.5rem] top-[10.7rem] w-60 text-center text-xs uppercase">
          {safeJsonParse(data?.address_province, 'province_name')}
        </div>

        {/* Zip Code */}
        <div className="absolute left-[36rem] top-[10.7rem] w-40 text-xs uppercase">{data?.address_zip_code}</div>

        {/* Years Residing */}
        <div className="absolute left-[46.5rem] top-[10.8rem] uppercase">
          {data?.address_length_of_stay || data?.year_residing || data?.permanent_address_length_of_stay}
        </div>

        {/* Home Ownership */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.residence_ownership === 'OWNED' ? 'left-[12.4rem] top-[12.8rem]' : 'hidden'
          }`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.residence_ownership === 'RENTED' ? 'left-[12.4rem] top-[14.2rem]' : 'hidden'
          }`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.residence_ownership === 'LIVING WITH RELATIVES'
              ? 'left-[12.4rem] top-[15.5rem]'
              : 'hidden'
          }`}
        />

        {/* Profession */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.occupation === 'FARMER' ? 'left-[32.5rem] top-[13.1rem]' : 'hidden'
          }`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.occupation !== 'FARMER' ? 'left-[32.5rem] top-[14.4rem]' : 'hidden'
          }`}
        />
        {/* Profession Other Details */}
        <div
          className={`absolute w-40 text-xs ${data?.occupation !== 'FARMER' ? 'left-[38.5rem] top-[14.8rem]' : 'hidden'}`}
        >
          {data?.occupation_title}
        </div>

        {/* Mobile Number */}
        <div className="absolute left-[12.4rem] top-[19.5rem] text-xs tracking-[1.25em]">
          {(data?.mobile_number || '').substring(2)}
        </div>

        {/* Tel Number */}
        <div className="absolute left-36 top-[21.4rem] w-64  text-center text-xs">{data?.telephone_number}</div>

        {/* Facebook Name */}
        <div className="absolute left-36 top-[23rem] w-64 text-center text-xs">{data?.facebook_name}</div>

        {/* Email */}
        <div className="bg-red absolute left-[33rem] top-[23.2rem] w-[250px] text-center text-xs">{email}</div>

        {/* TIN */}
        <div className="absolute left-[33rem] top-[17.4rem] w-[250px] ">{tin}</div>

        {/* Govt IDs */}
        <div className="absolute left-[32.5rem] top-[19.3rem] h-[51px] w-[265px] text-xs">
          {govtIdList.length > 0 && govtIdList.join(', ')}
        </div>

        {/* Civil Status */}
        {/* Married */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.civil_status === 'MARRIED' ? 'left-[14.1rem] top-[25.7rem]' : 'hidden'
          }`}
        />
        {/* Single */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.civil_status === 'SINGLE' ? 'left-[9.3rem] top-[25.7rem]' : 'hidden'
          }`}
        />
        {/* Widowed */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.civil_status === 'WIDOWED' ? 'left-[9.3rem] top-[27rem]' : 'hidden'
          }`}
        />
        {/* Separated */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.civil_status === 'SEPARATED' ? 'left-[9.3rem] top-[28.4rem]' : 'hidden'
          }`}
        />
        {/* Live in */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.civil_status === 'LIVE IN' ? 'left-[9.3rem] top-[29.8rem]' : 'hidden'
          }`}
        />

        {/* Name of Spouse */}
        <div className="absolute left-[23.5rem] top-[27.2rem] flex w-[400px] justify-between text-xs uppercase ">
          <div className="flex flex-1 justify-center">{spouse?.split(' ')[0] || ''}</div>
          <div className="flex flex-1 justify-center">{spouse?.split(' ')[1] || ''}</div>
          <div className="flex flex-1 justify-center">{spouse?.split(' ')[2] || ''}</div>
        </div>

        {/* Mobile Number of Spouse */}
        {/* {Missing} */}
        <div
          className="absolute left-[37.1rem] top-[30.15rem] w-[25rem] text-xs tracking-[1.3em]
        
        "
        >
          {data?.spouse_mobile_number?.substring(2)}
        </div>

        {/* Farm Location */}
        {/* Some Missing Fields */}
        {/* Address */}
        <div className="absolute left-48 top-[35.2rem] w-[11.3rem] text-center text-xs uppercase">
          {data?.farmerInfo?.farm_address_house_number}
        </div>

        {/* Street */}
        <div className="absolute left-[24.5rem] top-[35.2rem] w-[11.3rem] text-center text-xs uppercase">
          {data?.farmerInfo?.farm_address_street}
        </div>

        {/* Barangay */}
        <div className="absolute left-[37rem] top-[35.2rem] w-48 text-center text-xs uppercase">
          {safeJsonParse(data?.farm_address_barangay, 'brgy_name')}
        </div>

        {/* City */}
        <div className="absolute left-10 top-[38.2rem] w-60 text-center text-xs uppercase">
          {safeJsonParse(data?.farm_address_city, 'city_name')}
        </div>

        {/* Province */}
        <div className="absolute left-[18.5rem] top-[38.2rem] w-60  text-center text-xs uppercase">
          {safeJsonParse(data?.farm_address_province, 'province_name')}
        </div>

        {/* Zip Code */}
        <div className="absolute left-[35rem] top-[38.2rem] w-56 text-center text-xs uppercase">
          {data?.farm_address_zip_code}
        </div>

        {/* Farm Area */}
        <div className="absolute left-[12.5rem] top-[41.8rem] w-40 text-center text-xs uppercase">
          {data?.farmerInfo?.farm_area}
        </div>

        {/* Land Ownership */}
        {/* OWNED */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.farm_ownership === 1 ? 'left-[34.9rem] top-[40.9rem]' : 'hidden'
          }`}
        />
        {/* NOT OWNED */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.farm_ownership === 0 ? 'left-[34.9rem] top-[42.2rem]' : 'hidden'
          }`}
        />

        {/* Crops Planted */}
        {/* Palay Rice */}
        <Check strokeWidth={5} className={`absolute flex text-sm ${palayRice ? 'left-48 top-[46rem]' : 'hidden'}`} />

        {/* Corn */}
        <Check strokeWidth={5} className={`absolute flex text-sm ${corn ? 'left-48 top-[47.5rem]' : 'hidden'}`} />

        {/* Vegetable */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${vegetable ? 'left-[19.1rem] top-[46rem]' : 'hidden'}`}
        />

        {/* If has vegetable */}
        <div className="absolute left-[25.8rem] top-[46.2rem] w-[380px] text-clip text-xs uppercase">
          {(data?.cropsPlanted || '').map((row) => row?.crop.name).join(',')}
        </div>

        {/* Water Source */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.water_source?.split(', ').includes('Irrigation System') ? 'left-[32.2rem] top-[51.4rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.water_source?.split(', ').includes('Deep Well') ? 'left-[32.2rem] top-[52.7rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.water_source?.split(', ').includes('Rain-fed') ? 'left-[40.7rem] top-[51.5rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.water_source?.split(', ').includes('River Streams') ? 'left-[40.7rem] top-[52.7rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${hasOtherWaterSourceValue.filter((val) => !KNOWN_WATER_SOURCES.includes(val)) ? 'left-[32.2rem] top-[54rem]' : 'hidden'}`}
        />
        <div className="absolute left-[37rem] top-[54.4rem] w-[200px] text-center text-sm">
          {hasOtherWaterSourceValue.filter((val) => !KNOWN_WATER_SOURCES.includes(val))[0]}
        </div>

        {/* Farm Implements */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.farm_implements?.split(', ').includes('Mechanized Equipment') ? 'left-[37.7rem] top-[56.2rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.farm_implements?.split(', ').includes('Water Pump') ? 'left-[37.7rem] top-[57.4rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.farm_implements?.split(', ').includes('Drip Irrigation') ? 'left-[37.7rem] top-[57.4rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${hasOtherFarmImplements.filter((val) => !KNOWN_WATER_SOURCES.includes(val)) ? 'left-[32.2rem] top-[60.1rem]' : 'hidden'}`}
        />
        <div className="absolute left-[37rem] top-[60.4rem] w-[200px] text-center text-sm">
          {hasOtherFarmImplements.filter((val) => !KNOWN_FARM_IMPLEMENTS.includes(val))[0]}
        </div>

        {/* Fertilizer Used */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.fertilizer_used === 1 ? 'left-[6.8rem] top-[56.2rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.fertilizer_used === 2 ? 'left-[6.8rem] top-[57.5rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.fertilizer_used === 3 ? 'left-[6.8rem] top-[58.7rem]' : 'hidden'}`}
        />

        {/* Pesticides Used */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.pesticide_used === 1 ? 'left-[22rem] top-[56.2rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.pesticide_used === 2 ? 'left-[22rem] top-[57.5rem]' : 'hidden'}`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${data?.farmerInfo?.pesticide_used === 3 ? 'left-[22rem] top-[58.7rem]' : 'hidden'}`}
        />
      </div>
    </div>
  );
};

export default Page1;
