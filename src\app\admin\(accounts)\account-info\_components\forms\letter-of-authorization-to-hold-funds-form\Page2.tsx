import React from 'react';

import { FormDimensions } from '@/lib/constants/enums';

const Page2 = ({ data }) => {
  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <div
      className={`relative flex flex-col border bg-[url(/assets/forms/letter-of-authorization-to-hold-funds-form/letter-of-authorization-to-hold-funds-form-2.png)] bg-contain bg-top bg-no-repeat capitalize print:border-none ${FormDimensions.LETTER}`}
    >
      <div className="absolute top-[6.2rem] flex">
        {/* Date */}
        <div className="absolute -top-2 left-36 w-[200px] text-sm">{formattedDate}</div>

        {/* Name */}
        <div className="absolute left-24 top-[49rem] w-[300px] uppercase">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>
      </div>
    </div>
  );
};

export default Page2;
