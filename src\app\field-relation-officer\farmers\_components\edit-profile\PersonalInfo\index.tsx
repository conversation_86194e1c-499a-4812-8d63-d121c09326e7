import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { personalInformationSchema, TPersonalInformationSchema } from '@/app/field-relation-officer/schemas';
import { IFarmerBase, IPersonalInformation, ISafeParseResult } from '@/app/field-relation-officer/types';
import useFarmer from '@/lib/hooks/fro/useFarmer';

import IdentificationDocs from './IdentificationDocs';
import PersonalDetails from './PersonalDetails';
import PresentAddress from './PresentAddress';

interface IPersonalInfoProps {
  data: IFarmerBase;
}

const PersonalInfo = ({ data: gStateFRO }: IPersonalInfoProps) => {
  const data = gStateFRO.farmer;
  const { updateFarmer } = useFarmer();

  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TPersonalInformationSchema>({
    resolver: zodResolver(personalInformationSchema),
    defaultValues: {
      // Personal Details
      firstName: data?.first_name || '',
      middleName: data?.middle_name || '',
      lastName: data?.last_name || '',
      birthDate: data?.birth_date || '',
      gender: data?.gender || '',
      nationality: data?.farmerInfo.nationality || 'Filipino',
      mobileNumber: data?.mobile_number || '',
      placeOfBirth: data?.place_of_birth || '',
      email: gStateFRO?.email || '',
      telephoneNumber: data?.telephone_number || '',
      facebookName: data?.facebook_name || '',
      civilStatus: data?.civil_status || '',
      spouseName: data?.civil_status === 'MARRIED' ? data?.spouse_name : '',
      spouseMobileNumber: data?.civil_status === 'MARRIED' ? data?.spouse_mobile_number : '',

      // Present Address
      addressHouseNumber: data?.address_house_number || '',
      addressStreet: data?.address_house_number || '',
      addressRegion: '',
      addressProvince: data?.address_province || '',
      addressCity: data?.address_city || '',
      addressBarangay: data?.address_barangay || '',
      addressZipCode: data?.address_zip_code || '',
      addressLengthOfStay: data?.address_length_of_stay?.toString() || '',
      residenceOwnership: data?.farmerInfo.residence_ownership || '',

      // Identification Docs
      governmentIdentification: data?.governmentIdentifications?.length
        ? data.governmentIdentifications.map((id) => ({
            governmentIdType: id.government_id_type || '',
            governmentIdNumber: id.government_id_number || '',
            upload: id.government_id_image || null,
          }))
        : [
            {
              governmentIdType: '',
              governmentIdNumber: '',
              upload: null,
            },
          ],
    },
  });

  const formSubmit = handleSubmit(async () => {
    const { data, success } = personalInformationSchema.safeParse(values()) as ISafeParseResult<IPersonalInformation>;

    if (success) {
      const governmentIdUploads: Record<string, File> = {};

      if (Array.isArray(data.governmentIdentification)) {
        data.governmentIdentification.forEach((row: any) => {
          const key = `governmentIdentification_${row.governmentIdNumber}`;

          if (row.upload instanceof FileList && row.upload[0]) {
            governmentIdUploads[key] = row.upload[0];
          } else if (row.upload instanceof File) {
            governmentIdUploads[key] = row.upload;
          }
        });
      }

      const payload = { ...data, ...governmentIdUploads };
      await updateFarmer(payload);
      console.log('PersonalInfo:', payload, governmentIdUploads, { ids: data.governmentIdentification });
    }
  });

  return (
    <form onSubmit={formSubmit}>
      <PersonalDetails register={register} control={control} errors={errors} setValue={setValue} watch={watch} />
      <PresentAddress register={register} control={control} errors={errors} values={values} setValue={setValue} />
      <IdentificationDocs register={register} control={control} errors={errors} watch={watch} values={values} />

      <div className="mt-16 flex justify-end gap-4">
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
};

export default PersonalInfo;
