'use client';

import React, { useState } from 'react';

import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import FarmLandLeaseAgreementForm from '@/app/admin/(accounts)/account-info/_components/forms/farm-land-lease-agreement-form/page';
import KitaForm from '@/app/admin/(accounts)/account-info/_components/forms/kita-form/KitaForm';
import LandbankForm from '@/app/admin/(accounts)/account-info/_components/forms/LandbankForm';
import LBPDataPrivacyForm from '@/app/admin/(accounts)/account-info/_components/forms/LBPDataPrivacyForm';
import LetterOfAuhorizationForm from '@/app/admin/(accounts)/account-info/_components/forms/letter-of-authorization-to-hold-funds-form/LetterOfAuhorizationForm';
import LbpAgrisensoForm from '@/app/admin/(accounts)/account-info/_components/forms/LPBAgrisensoForm';
import MsaForm from '@/app/admin/(accounts)/account-info/_components/forms/msa-form/msa-form';
import PrintFarmPlan from '@/app/admin/(accounts)/account-info/_components/forms/print-farm-plan';
import PtmaForm from '@/app/admin/(accounts)/account-info/_components/forms/ptma-form/ptma-form';
import TripartiteAgreementForm from '@/app/admin/(accounts)/account-info/_components/forms/tripartite-agreement-form/TripartiteAgreementForm';

type TFormModalComponent = React.ComponentType<{
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
  farmPlan: any;
}>;

interface IFormConfigs {
  value: string;
  label: string;
  form: TFormModalComponent;
}

export const formConfigs: IFormConfigs[] = [
  {
    value: 'kita-form',
    label: 'KITA FORM',
    form: KitaForm,
  },
  {
    value: 'lbp-cis-form',
    label: 'LBP CIS FORM',
    form: LandbankForm,
  },
  {
    value: 'lbp-data-privacy-form',
    label: 'Data Privacy LBP Form',
    form: LBPDataPrivacyForm,
  },
  {
    value: 'letter-of-authorization-form',
    label: 'Letter of Authorization to Hold Funds Form',
    form: LetterOfAuhorizationForm,
  },
  {
    value: 'tripartite-agreement-form',
    label: 'Tripartite Agreement Form',
    form: TripartiteAgreementForm,
  },
  {
    value: 'ptma-form',
    label: 'PTMA Form',
    form: PtmaForm,
  },
  {
    value: 'msa-form',
    label: 'MSA Form',
    form: MsaForm,
  },
  {
    value: 'farm-land-lease-agreement-form',
    label: 'Farm Land Lease Agreement Form',
    form: FarmLandLeaseAgreementForm,
  },
  {
    value: 'lbp-agrisenso-form',
    label: 'LBP Agrisenso Form',
    form: LbpAgrisensoForm,
  },
  {
    value: 'farm-plan',
    label: 'Farm Plan',
    form: PrintFarmPlan,
  },
];

const GenerateForm = ({ data, farmPlan }: { data: any; farmPlan: any }) => {
  const [activeForm, setActiveForm] = useState<string | null>(null);

  console.log('farmerData2', data);

  return (
    <>
      <Select value="" onValueChange={(v) => setActiveForm(v)}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Print Document" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {formConfigs.map(({ value, label }) => (
              <SelectItem key={value} value={value} className="focus:bg-[#2F80ED] focus:text-white">
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      {formConfigs
        .filter(({ value }) => value === activeForm)
        .map(({ value, form: FormComponent }) => (
          <FormComponent
            key={value}
            isDialogOpen={activeForm === value}
            setIsDialogOpen={(isOpen: boolean) => setActiveForm(isOpen ? value : null)}
            data={data}
            farmPlan={farmPlan}
          />
        ))}
    </>
  );
};

export default GenerateForm;
