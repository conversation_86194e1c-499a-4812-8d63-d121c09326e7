import React, { useEffect } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  useFormContext,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { OPTIONS_SOURCE_OF_FUNDS } from '@/app/admin/(accounts)/account-info/_components/tab-content/account-profile/Enums';
import { cn } from '@/lib/utils';

import { TBusinessInfoSchema } from '../../schemas';

interface IFinancialInformationProps {
  register: UseFormRegister<TBusinessInfoSchema>;
  control: Control<TBusinessInfoSchema>;
  errors: FieldErrors<TBusinessInfoSchema>;
}

const FinancialInformation = ({ register, control, errors }: IFinancialInformationProps) => {
  return (
    <div>
      <FormTitle title="Farm Location" />
      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="sourceOfFunds" label="Source of Income" errors={errors} required>
          <Controller
            control={control}
            name="sourceOfFunds"
            render={({ field: { onChange, value } }) => {
              const existingValues = Array.isArray(value) ? value : (value || '').split(',').map((v) => v.trim());
              const currentOptions = existingValues.filter((v) => v).map((v) => ({ label: v, value: v }));

              const mergedOptions = [
                ...OPTIONS_SOURCE_OF_FUNDS,
                ...currentOptions.filter(
                  (opt) => opt.value && opt.label && !OPTIONS_SOURCE_OF_FUNDS.some((def) => def.value === opt.value),
                ),
              ];
              return (
                <MultipleSelector
                  value={currentOptions}
                  onChange={(selected) => onChange(selected.map((item) => item.value))}
                  defaultOptions={mergedOptions}
                  placeholder="Select from selection or create new"
                  creatable
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              );
            }}
          />
        </FormField>

        <FormField name="monthlyGrossIncome" label="Monthly Gross Income" errors={errors} required>
          <Input
            {...register('monthlyGrossIncome', { required: 'Monthly Gross Income is required' })}
            type="number"
            min={0}
            placeholder="Enter Monthly Gross Income"
            className={cn('focus-visible:ring-primary', errors.monthlyGrossIncome && 'border-red-500')}
          />
        </FormField>
      </div>
    </div>
  );
};

export default FinancialInformation;
