import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { personalInformationSchema, TPersonalInformationSchema } from '../../schemas';
import { IFROState, IPersonalInformation, ISafeParseResult } from '../../types';
import IdentificationDocs from './IdentificationDocs';
import PersonalDetails from './PersonalDetails';
import PresentAddress from './PresentAddress';

interface IPersonalInfoProps {
  gStateFRO: State<IFROState, {}>;
  onPrevious: () => void;
  onNext: () => void;
  onClear: () => void;
}

const PersonalInfo = ({ gStateFRO, onPrevious, onNext, onClear }: IPersonalInfoProps) => {
  const data = gStateFRO?.form.step2?.value;
  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TPersonalInformationSchema>({
    resolver: zodResolver(personalInformationSchema),
    defaultValues: {
      // Personal Details
      firstName: data?.firstName || '',
      middleName: data?.middleName || '',
      lastName: data?.lastName || '',
      birthDate: data?.birthDate || '',
      gender: data?.gender || '',
      nationality: 'Filipino',
      mobileNumber: data?.mobileNumber || '',
      placeOfBirth: data?.placeOfBirth || '',
      email: data?.email || '',
      telephoneNumber: data?.telephoneNumber || '',
      facebookName: data?.facebookName || '',
      civilStatus: data?.civilStatus || '',
      spouseName: data?.civilStatus === 'MARRIED' ? data?.spouseName : '',
      spouseMobileNumber: data?.civilStatus === 'MARRIED' ? data?.spouseMobileNumber : '',

      // Present Address
      addressHouseNumber: data?.addressHouseNumber || '',
      addressStreet: data?.addressStreet || '',
      addressRegion: data?.addressRegion || '',
      addressProvince: data?.addressProvince || '',
      addressCity: data?.addressCity || '',
      addressBarangay: data?.addressBarangay || '',
      addressZipCode: data?.addressZipCode || '',
      addressLengthOfStay: data?.addressLengthOfStay || '',
      residenceOwnership: data?.residenceOwnership || '',

      // Identification Docs
      governmentIdentification: data?.governmentIdentification
        ? Array.from(data.governmentIdentification)
        : [
            {
              governmentIdType: '',
              governmentIdNumber: '',
              upload: null,
            },
          ],
    },
  });

  const handleNext = handleSubmit(async () => {
    const { data, success } = personalInformationSchema.safeParse(values()) as ISafeParseResult<IPersonalInformation>;

    if (success) {
      // NOTE: saving base64 to localStorage (saving more than 1 image is already hitting the quota of 5mb)
      // const filteredIds = data.governmentIdentification.map(async (r) => {
      //   const uploadBase64 = r.upload ? await fileToBase64(r.upload[0] as File) : null;
      //   return { ...r, upload: uploadBase64 };
      // });

      // const promiseIds = await Promise.all(filteredIds)
      gStateFRO.form.step2.set(data);
      onNext();
    }
  });

  const handlePrevious = () => {
    const { data, success } = personalInformationSchema.safeParse(values()) as ISafeParseResult<IPersonalInformation>;

    if (success) {
      gStateFRO.form.step2.set(data);
    }
    onPrevious();
  };

  console.log('errors', errors);

  return (
    <form onSubmit={handleNext}>
      <PersonalDetails
        register={register}
        control={control}
        errors={errors}
        setValue={setValue}
        watch={watch}
        values={values}
      />
      <PresentAddress register={register} control={control} errors={errors} values={values} />
      <IdentificationDocs register={register} control={control} errors={errors} watch={watch} values={values} />

      <div className="mt-16 flex justify-between">
        <Button type="button" variant="outline" onClick={onClear}>
          Clear All Forms
        </Button>
        <div className="flex justify-center gap-4">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit">Next</Button>
        </div>
      </div>
    </form>
  );
};

export default PersonalInfo;
