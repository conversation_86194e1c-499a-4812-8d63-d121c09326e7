import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React, { useRef } from 'react';
import { useForm } from 'react-hook-form';
import SignatureCanvas from 'react-signature-canvas';

import FormTitle from '@/components/common/forms/form-title';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { base64toFile, cn } from '@/lib/utils';

import { dataPrivacySchema, TDataPrivacySchema } from '../../schemas';
import { IDataPrivacy, IFROState, ISafeParseResult } from '../../types';

interface IDataPrivacyProps {
  onNext: () => void;
  gStateFRO: State<IFROState, LocalStored>;
  onClear: () => void;
}

const DataPrivacy = ({ onNext, gStateFRO, onClear }: IDataPrivacyProps) => {
  const sigCanvasRef = useRef<SignatureCanvas>(null);
  const data = gStateFRO?.form.step1?.value;

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues: values,
    setValue,
  } = useForm<TDataPrivacySchema>({
    resolver: zodResolver(dataPrivacySchema),
    defaultValues: {
      isAgreeUsingData: !!(data && data.isAgreeUsingData === 1),
      isAgreeVisitingFarm: !!(data && data.isAgreeVisitingFarm === 1),
      isAgreeSharingData: !!(data && data.isAgreeSharingData === 1),
      userImage: data?.userImage instanceof FileList ? data.userImage[0] : (data?.userImage as File | null) || null,
      signature: (data?.signature as File) || null,
    },
  });

  const handleSignatureEnd = async () => {
    const base64 = sigCanvasRef.current?.toDataURL('image/png');
    const signatureFile = await base64toFile(base64, 'signature.png');
    setValue('signature', signatureFile);
  };

  const handleClearSignature = () => {
    sigCanvasRef.current?.clear();
    setValue('signature', null);
  };

  const handleNext = handleSubmit(async () => {
    const { data, success } = dataPrivacySchema.safeParse(values()) as ISafeParseResult<IDataPrivacy>;

    if (success) {
      gStateFRO.form.step1.set(data);
      onNext();
    }
  });

  return (
    <div>
      <FormTitle title="Consent Declaration and Data Privacy Policy Statement" />
      <form onSubmit={handleNext}>
        <div className="flex flex-col gap-16">
          <div className="flex flex-col gap-6">
            <div className="flex items-start gap-3">
              <input type="checkbox" {...register('isAgreeUsingData', { required: true })} id="isAgreeUsingData" />
              <p className="text-sm">
                Sumasang-ayon ako na maaaring gamitin ng{' '}
                <strong>KITA AGRITECH CORPORATION (&quot;KITA AGRITECH&quot;)</strong> ang impormasyong ito para sa
                pagpaparehistro ko sa Farmer Kita Program, alinsunod sa Data Privacy Policy Statement na nasa ibaba.
              </p>
            </div>
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                {...register('isAgreeVisitingFarm', { required: true })}
                id="isAgreeVisitingFarm"
              />
              <p className="text-sm">
                Pinapayagan ko ang <strong>KITA AGRITECH</strong> na bumisita sa aking sakahan at ako ay magbibigay ng
                karagdagang mga dokumento, at pahintulot sa pagkuha ng mga larawan at bidyo, upang beripikahin ang
                impormasyong aking inilagay sa form na ito.
              </p>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" {...register('isAgreeSharingData', { required: true })} id="isAgreeSharingData" />
              <p className="text-sm">
                Pinapayagan ko ang <strong>KITA AGRITECH</strong> na ibahagi ang mga impormasyong aking ibinigay sa mga
                Partner Financial Institutions nito kung sakali man na ako ay mag-apply para sa Farm Production Loan
                bilang bahagi ng KYC procedures nito.
              </p>
            </div>
            {(errors.isAgreeUsingData || errors.isAgreeVisitingFarm || errors.isAgreeSharingData) && (
              <p className="text-sm text-red-500">All checkboxes must be checked to proceed.</p>
            )}
          </div>

          <div className="mx-auto grid w-full items-center gap-3 text-center">
            <Label htmlFor="userImage">
              Take a photo of the farmer holding the signed Consent and Data Privacy Statement.
            </Label>
            <Input
              id="userImage"
              type="file"
              className="mx-auto max-w-sm"
              {...register('userImage', { required: true })}
            />
            {errors.userImage && <p className="text-sm text-red-500">{errors.userImage.message}</p>}
          </div>

          <div className={cn('justify-center gap-4 hidden', (data?.userImage || data?.signature) && 'flex')}>
            {values().userImage instanceof File && (
              <img
                src={URL.createObjectURL(values().userImage)}
                alt="Farmer Photo"
                width={200}
                height={200}
                className="object-cover"
              />
            )}

            {values().signature instanceof File && (
              <img
                src={URL.createObjectURL(values().signature)}
                alt="Signature"
                width={250}
                height={200}
                className="scale-75 object-cover"
              />
            )}
          </div>

          <Card>
            <CardContent className="space-y-4 p-4">
              {errors.signature && <p className="text-sm text-red-500">{errors.signature.message}</p>}
              <div className="rounded-md border border-gray-300">
                <SignatureCanvas
                  ref={sigCanvasRef}
                  penColor="black"
                  canvasProps={{ className: 'w-full h-40 rounded-md' }}
                  onEnd={handleSignatureEnd}
                />
              </div>
              <div className="flex gap-2">
                <Button variant="outline" type="button" onClick={handleClearSignature}>
                  Clear
                </Button>
              </div>
            </CardContent>
          </Card>
          <div className="mt-8 flex justify-between">
            <Button type="button" variant="outline" onClick={onClear}>
              Clear All Forms
            </Button>
            <div className="flex justify-center gap-4">
              <Button type="submit">Next</Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default DataPrivacy;
