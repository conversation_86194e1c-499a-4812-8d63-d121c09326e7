'use client';

import { format } from 'date-fns';
import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';

import { FormDimensions } from '@/lib/constants/enums';

interface FarmPlanSubItem {
  id: number;
  farm_plan_item_id: number;
  farm_plan_id: number;
  expected_date: string;
  item_name: string;
  unit: string;
  quantity: number;
  unit_cost: number;
  total_amount: number;
  notes: string;
  marketplace_product_id: number;
  created_at: string;
  updated_at: string;
}

interface GroupedItems {
  [expectedDate: string]: {
    [category: string]: FarmPlanSubItem[];
  };
}

export default function PrintFarmPlan({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  console.log('farmerData', farmerData);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: `FarmPlan_${farmerData?.farmer?.first_name}_${farmerData?.farmer?.last_name}_${format(new Date(), 'yyyy-MM-dd')}`,
    pageStyle: `
      @page {
        margin: 12mm 14mm 20mm 14mm;
      }
    `,
  });

  const farmPlan = farmerData?.farmPlans?.[farmerData.farmPlans.length - 1];
  console.log('farmPlan', farmPlan);

  const address = farmerData && farmerData['farmer']['address'] ? JSON.parse(farmerData['farmer']['address']) : {};

  // Group farm plan sub items by expected date and category
  const groupedItems: GroupedItems = {};

  farmPlan.farmPlanItems.forEach((item: any) => {
    if (Array.isArray(item.farmPlanSubItems)) {
      item.farmPlanSubItems.forEach((subItem: FarmPlanSubItem) => {
        const expectedDate = subItem.expected_date;
        const category = item.name;

        if (!groupedItems[expectedDate]) {
          groupedItems[expectedDate] = {};
        }
        if (!groupedItems[expectedDate][category]) {
          groupedItems[expectedDate][category] = [];
        }

        groupedItems[expectedDate][category].push(subItem);
      });
    }
  });

  // Calculate subtotals for each group
  const calculateSubtotal = (items: FarmPlanSubItem[]) => {
    return items.reduce((sum, item) => sum + item.total_amount, 0);
  };

  // Calculate grand total
  // const grandTotal = farmPlan.total_amount || 0;

  // const farmer = farmPlan.user.farmer;

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Farm Plan</DialogTitle>
          </DialogHeader>
          <div className="mx-auto flex max-h-[50vh] max-w-4xl flex-1 justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div ref={contentRef}>
              <div className={`relative flex ${FormDimensions.LETTER} bordercapitalize flex-col`}>
                {/* Header */}
                <div className="header mb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <img className="h-14" src="/kita-logo.png" alt="KITA Logo" />
                    </div>

                    <div className="text-right text-xs italic">
                      <div>{`Date & Time Generated: ${format(new Date(), 'MMM dd, yyyy - h:mm a')}`}</div>
                      {/* <div className="mt-0.5">{`Farm Plan ID - ${farmPlan.reference_number}`}</div> */}
                    </div>
                  </div>
                </div>

                <h1 className="mb-6 text-center text-lg font-bold text-kitaph-blue">
                  Farmer Kita Program
                  <br />
                  LANDBANK AGRISENSO PLUS PRODUCTION LOAN
                </h1>

                {/* Farmer Information */}
                <div className="mb-6 space-y-2 text-sm">
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Farmer Name:</span>
                    </div>
                    <div className="col-span-4">{`${farmerData?.farmer?.first_name} ${farmerData?.farmer?.last_name}`}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Home Address:</span>
                    </div>
                    <div className="col-span-4">
                      {`${address?.addressHouseNumber}, ${address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}, ${address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ''}, ${address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''}`}
                    </div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Farm Address:</span>
                    </div>
                    <div className="col-span-4">{farmerData?.farmer?.farmerInfo?.farm_address}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">No. of Hectares:</span>
                    </div>
                    <div className="col-span-4">{farmerData?.farmer?.farmerInfo?.farm_area}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Crop:</span>
                    </div>
                    <div>{farmPlan.crop?.name || 'Lettuce'}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Cropping Type:</span>
                    </div>
                    <div>{farmPlan.cropping_type}</div>
                  </div>
                </div>

                {Object.entries(groupedItems).map(([expectedDate, categories]) =>
                  Object.entries(categories).map(([category, items]) => {
                    const subtotal = calculateSubtotal(items);

                    return (
                      <div key={`${expectedDate}-${category}`} className="mb-6">
                        <div className="bg-kitaph-blue py-2 text-center text-sm font-medium text-white">
                          INPUTS: {category} (₱)
                        </div>

                        <table className="w-full border-collapse border border-gray-400 text-xs">
                          <thead>
                            <tr className="bg-kitaph-blue text-white">
                              <th className="border border-gray-400 p-2 text-left">Expected Date</th>
                              <th className="border border-gray-400 p-2 text-left">Items</th>
                              <th className="border border-gray-400 p-2 text-center">Unit</th>
                              <th className="border border-gray-400 p-2 text-center">Quantity</th>
                              <th className="border border-gray-400 p-2 text-center">Unit Cost</th>
                              <th className="border border-gray-400 p-2 text-center">Total Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {items.map((item, index) => (
                              <tr key={item.id}>
                                <td className="border border-gray-400 p-2">
                                  {index === 0 ? format(new Date(expectedDate), 'MM/dd/yyyy') : ''}
                                </td>
                                <td className="border border-gray-400 p-2">{item.item_name}</td>
                                <td className="border border-gray-400 p-2 text-center">{item.unit}</td>
                                <td className="border border-gray-400 p-2 text-center">{item.quantity}</td>
                                <td className="border border-gray-400 p-2 text-center">{item.unit_cost.toFixed(2)}</td>
                                <td className="border border-gray-400 p-2 text-center">
                                  {item.total_amount.toFixed(2)}
                                </td>
                              </tr>
                            ))}
                            <tr>
                              <td colSpan={5} className="border border-gray-400 p-2 text-right font-medium">
                                Sub Total
                              </td>
                              <td className="border border-gray-400 p-2 text-center font-medium">
                                {subtotal.toFixed(2)}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    );
                  }),
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
