'use client';

import { format } from 'date-fns';
import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from '@/components/ui/dialog';

import { FormDimensions } from '@/lib/constants/enums';

interface FarmPlanSubItem {
  id: number;
  farm_plan_item_id: number;
  farm_plan_id: number;
  expected_date: string;
  item_name: string;
  unit: string;
  quantity: number;
  unit_cost: number;
  total_amount: number;
  notes: string;
  marketplace_product_id: number;
  created_at: string;
  updated_at: string;
}

interface FarmPlanItem {
  id: number;
  farm_plan_id: number;
  name: string;
  slug: string;
  type: string;
  total_amount: number | null;
  created_at: string;
  updated_at: string;
  farmPlanSubItems: FarmPlanSubItem[];
}

interface GroupedItems {
  [expectedDate: string]: {
    [category: string]: FarmPlanSubItem[];
  };
}

interface FarmerData {
  farmer: {
    first_name: string;
    last_name: string;
    address?: string;
    farmerInfo?: {
      farm_address?: string;
      farm_area?: string;
    };
  };
  farmPlans: Array<{
    reference_number: string;
    total_amount: number;
    cropping_type: string;
    agronomist_name: string;
    agronomist_prc_number: string;
    agronomist_valid_until: string;
    head_agronomist_name: string;
    head_agronomist_prc_number: string;
    head_agronomist_valid_until: string;
    crop?: {
      name: string;
    };
    farmPlanItems: FarmPlanItem[];
  }>;
}

interface PrintFarmPlanProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: FarmerData;
}

export default function PrintFarmPlan({ isDialogOpen, setIsDialogOpen, data: farmerData }: PrintFarmPlanProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  const farmPlan = farmerData?.farmPlans?.[farmerData.farmPlans.length - 1];
  const address = farmerData?.farmer?.address ? JSON.parse(farmerData.farmer.address) : {};

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: `FarmPlan_${farmerData?.farmer?.first_name}_${farmerData?.farmer?.last_name}_${farmPlan?.reference_number}_${format(new Date(), 'yyyy-MM-dd')}`,
    pageStyle: `
       @media print {
          html,
          body {
            height: auto !important;
            overflow: visible !important;
            -webkit-print-color-adjust: exact;
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'DM Sans', sans-serif !important;
          }

          * {
            font-family: 'DM Sans', sans-serif !important;
          }

          @page {
            size: LETTER;
            margin: 12mm 14mm 20mm 14mm;
          }

          .header {
            margin-bottom: 1.5rem;
          }

          .signature-section {
            page-break-inside: avoid;
            break-inside: avoid;
            margin-top: 2rem;
          }

          table {
            page-break-inside: avoid;
            break-inside: avoid;
          }
        }
    `,
  });

  // Group farm plan sub items by expected date and category
  const groupedItems: GroupedItems = {};

  farmPlan?.farmPlanItems?.forEach((item: FarmPlanItem) => {
    if (Array.isArray(item.farmPlanSubItems)) {
      item.farmPlanSubItems.forEach((subItem: FarmPlanSubItem) => {
        const expectedDate = subItem.expected_date;
        const category = item.name;

        if (!groupedItems[expectedDate]) {
          groupedItems[expectedDate] = {};
        }
        if (!groupedItems[expectedDate][category]) {
          groupedItems[expectedDate][category] = [];
        }

        groupedItems[expectedDate][category].push(subItem);
      });
    }
  });

  // Calculate subtotals for each group
  const calculateSubtotal = (items: FarmPlanSubItem[]) => {
    return items.reduce((sum, item) => sum + item.total_amount, 0);
  };

  // Calculate grand total
  const grandTotal = farmPlan.total_amount || 0;

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Farm Plan</DialogTitle>
          </DialogHeader>
          <div className="mx-auto flex max-h-[50vh] max-w-4xl flex-1 justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div ref={contentRef}>
              <div className={`relative flex ${FormDimensions.LETTER} bordercapitalize flex-col`}>
                {/* Header */}
                <div className="header mb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <img className="h-14" src="/kita-logo.png" alt="KITA Logo" />
                    </div>

                    <div className="text-right text-xs italic">
                      <div>{`Date & Time Generated: ${format(new Date(), 'MMM dd, yyyy - h:mm a')}`}</div>
                      <div className="mt-0.5">{`Farm Plan ID - ${farmPlan.reference_number}`}</div>
                    </div>
                  </div>
                </div>

                <h1 className="mb-6 text-center text-lg font-bold text-kitaph-blue">
                  Farmer Kita Program
                  <br />
                  LANDBANK AGRISENSO PLUS PRODUCTION LOAN
                </h1>

                {/* Farmer Information */}
                <div className="mb-6 space-y-2 text-sm">
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Farmer Name:</span>
                    </div>
                    <div className="col-span-4">{`${farmerData?.farmer?.first_name} ${farmerData?.farmer?.last_name}`}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Home Address:</span>
                    </div>
                    <div className="col-span-4">
                      {`${address?.addressHouseNumber}, ${address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}, ${address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ''}, ${address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''}`}
                    </div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Farm Address:</span>
                    </div>
                    <div className="col-span-4">{farmerData?.farmer?.farmerInfo?.farm_address}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">No. of Hectares:</span>
                    </div>
                    <div className="col-span-4">{farmerData?.farmer?.farmerInfo?.farm_area}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Crop:</span>
                    </div>
                    <div>{farmPlan.crop?.name || 'Lettuce'}</div>
                  </div>
                  <div className="grid grid-cols-5 gap-x-8">
                    <div>
                      <span className="font-medium">Cropping Type:</span>
                    </div>
                    <div>{farmPlan.cropping_type}</div>
                  </div>
                </div>

                {Object.entries(groupedItems).map(([expectedDate, categories]) =>
                  Object.entries(categories).map(([category, items]) => {
                    const subtotal = calculateSubtotal(items);

                    return (
                      <div key={`${expectedDate}-${category}`} className="mb-6 mt-4">
                        <div className="bg-kitaph-blue py-2 text-center text-sm font-medium text-white">
                          INPUTS: {category} (₱)
                        </div>

                        <table className="w-full border-collapse border border-gray-400 text-xs">
                          <thead>
                            <tr className="bg-kitaph-blue text-white">
                              <th className="border border-gray-400 p-2 text-left">Expected Date</th>
                              <th className="border border-gray-400 p-2 text-left">Items</th>
                              <th className="border border-gray-400 p-2 text-center">Unit</th>
                              <th className="border border-gray-400 p-2 text-center">Quantity</th>
                              <th className="border border-gray-400 p-2 text-center">Unit Cost</th>
                              <th className="border border-gray-400 p-2 text-center">Total Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {items.map((item, index) => (
                              <tr key={item.id}>
                                <td className="border border-gray-400 p-2">
                                  {index === 0 ? format(new Date(expectedDate), 'MM/dd/yyyy') : ''}
                                </td>
                                <td className="border border-gray-400 p-2">{item.item_name}</td>
                                <td className="border border-gray-400 p-2 text-center">{item.unit}</td>
                                <td className="border border-gray-400 p-2 text-center">{item.quantity}</td>
                                <td className="border border-gray-400 p-2 text-center">{item.unit_cost.toFixed(2)}</td>
                                <td className="border border-gray-400 p-2 text-center">
                                  {item.total_amount.toFixed(2)}
                                </td>
                              </tr>
                            ))}
                            <tr>
                              <td colSpan={5} className="border border-gray-400 p-2 text-right font-medium">
                                Sub Total
                              </td>
                              <td className="border border-gray-400 p-2 text-center font-medium">
                                {subtotal.toFixed(2)}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    );
                  }),
                )}

                {/* Grand Total */}
                <div className="mb-8">
                  <div className="flex justify-end">
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        <span className="mr-8">Grand Total</span>
                        <span className="font-bold">
                          {grandTotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Signature Section */}
                <div className="signature-section mt-16">
                  <div className="mb-16 grid grid-cols-2 gap-8">
                    <div className="border border-gray-500 p-4">
                      <div className="mb-2 text-sm font-medium">Prepared By</div>
                      <div className="mb-4 h-16"></div>
                      <div className="border-t border-gray-400 pt-2 text-center">
                        <div className="text-sm font-medium">{farmPlan.agronomist_name}</div>
                        <div className="mt-2 text-xs">
                          <div>PRC License No.: {farmPlan.agronomist_prc_number}</div>
                          <div>Valid Until: {farmPlan.agronomist_valid_until}</div>
                        </div>
                      </div>
                    </div>

                    <div className="border border-gray-500 p-4">
                      <div className="mb-2 text-sm font-medium">Noted By</div>
                      <div className="mb-4 h-16"></div>
                      <div className="border-t border-gray-400 pt-2 text-center">
                        <div className="text-sm font-medium">{farmPlan.head_agronomist_name}</div>
                        <div className="mt-2 text-xs">
                          <div>PRC License No.: {farmPlan.head_agronomist_prc_number}</div>
                          <div>Valid Until: {farmPlan.head_agronomist_valid_until}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Agreement Text */}
                  <div className="mb-8 text-center text-sm">
                    <p>I hereby agree to this Farm Plan and Budget in relation to my farm loan application</p>
                    <p>with LANDBANK AGRISENSO PLUS.</p>
                  </div>

                  {/* Farmer Signature */}
                  <div className="text-center">
                    <div className="inline-block">
                      <div className="mb-2 h-16 w-64"></div>
                      <div className="border-t border-gray-400 text-center">
                        <div className="pt-2 text-sm font-medium">{`${farmerData.farmer.first_name} ${farmerData.farmer.last_name}`}</div>
                        <div className="text-xs">Partner - Farmer</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
