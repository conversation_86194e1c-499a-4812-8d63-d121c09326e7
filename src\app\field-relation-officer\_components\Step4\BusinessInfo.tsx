import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { businessInfoSchema, TBusinessInfoSchema } from '../../schemas';
import { IBusinessInfo, IFROState, ISafeParseResult } from '../../types';
import FarmBusinessInformation from './FarmBusinessInformation';
import FinancialInformation from './FinancialInformation';
import PurchaserInformation from './PurchaserInformation';

interface IBusinessInfoProps {
  gStateFRO: State<IFROState, {}>;
  onPrevious: () => void;
  onClear: () => void;
  onSave: () => void;
}

const BusinessInfo = ({ gStateFRO, onPrevious, onClear, onSave }: IBusinessInfoProps) => {
  const data = gStateFRO?.form.step4?.value;
  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TBusinessInfoSchema>({
    resolver: zodResolver(businessInfoSchema),
    defaultValues: {
      // Financial Information
      sourceOfFunds: data?.sourceOfFunds || '',
      monthlyGrossIncome: data?.monthlyGrossIncome || 0,

      // Farm Business Information
      isMemberOfOrganization: data?.isMemberOfOrganization === '1' ? '1' : '0',
      organizationName: data?.organizationName || '',
      organizationPosition: data?.organizationPosition || '',
      hasPastFarmLoans: data?.hasPastFarmLoans === '1' ? '1' : '0',
      pastFarmLoans: data?.pastFarmLoans || '',
      hasPastFarmLoanPaid: data?.hasPastFarmLoanPaid === '1' ? '1' : '0',
      hasNeedFarmLoan: data?.hasNeedFarmLoan === '1' ? '1' : '0',
      needFarmLoanReason: data?.needFarmLoanReason || '',
      isInterestedToSellAtTradingPost: data?.isInterestedToSellAtTradingPost === '1' ? '1' : '0',

      // Purchaser Information
      purchaserSellingLocation: data?.purchaserSellingLocation || '',
      purchaserFullname: data?.purchaserFullname || '',
      purchaserContactNumber: data?.purchaserContactNumber || '',
    },
  });

  const handleNext = handleSubmit(() => {
    const { data, success } = businessInfoSchema.safeParse(values()) as ISafeParseResult<IBusinessInfo>;

    if (success) {
      gStateFRO.form.step4.set(data);
      onSave();
    }
  });

  const handlePrevious = () => {
    const { data, success } = businessInfoSchema.safeParse(values()) as ISafeParseResult<IBusinessInfo>;

    if (success) {
      gStateFRO.form.step4.set(data);
    }
    onPrevious();
  };

  return (
    <form onSubmit={handleNext}>
      <FinancialInformation register={register} control={control} errors={errors} />
      <FarmBusinessInformation register={register} control={control} errors={errors} watch={watch} />
      <PurchaserInformation register={register} control={control} errors={errors} watch={watch} />
      <div className="mt-16 flex justify-between">
        <Button type="button" variant="outline" onClick={onClear}>
          Clear All Forms
        </Button>
        <div className="flex justify-center gap-4">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </div>
    </form>
  );
};

export default BusinessInfo;
